% PRIVATE
%
% Files
%   AppendData             - AppendData adds data to an existing variable in a v6 MAT-file
%   ByteAlign              - ByteAlign aligns the file position to an 8 byte boundary
%   ChangeDimensions       - ChangeDimensions is called to increase the size of an existing variable

%   GetSmallDataElement    - GetSmallDataElement returns a small data element from a MAT-file
%   PadToEightByteBoundary - PadToEightByteBoundary does what its name suggests
%   sizeof                 - returns the size in bytes of the class
%   StandardMiCodes        - return Matlab standard codes for data formats
%   StandardMxCodes        - return Matlab standard codes for data formats
%   argcheck               - argcheck does error checking for the MAT-file utilities
