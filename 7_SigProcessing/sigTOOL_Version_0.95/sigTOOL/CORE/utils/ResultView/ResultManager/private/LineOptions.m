function tp=LineOptions(tp)
Foreground=java.awt.Color(64/255,64/255,122/255);
Background=java.awt.Color(1,1,0.9);

p=uipanel('Parent', tp.Panel.uipanel,...
    'Units', 'normalized',...
    'Position',[0.05 0.75 0.85 0.125]);
tp.LineOptions.Panel=jcontrol(p, 'javax.swing.JPanel',...
    'Units', 'normalized',...
    'Position',[0 0 1 1],...
    'Foreground', Foreground,...
    'Background', Background,...
    'Border',javax.swing.BorderFactory.createTitledBorder('Line Options'));

tp.LineOptions.ColorChooser=jcontrol(tp.LineOptions.Panel, 'javax.swing.JButton',...
    'Units', 'normalized',...
    'Position',[0.2 0.55 0.6 0.2],...
    'Text', 'Color');
tp.LineOptions.ColorChooser.MouseClickedCallback=@LineColorCallback;

tp.LineOptions.LineWidth=jcontrol(tp.LineOptions.Panel, 'javax.swing.JComboBox',...
        'Units', 'normalized',...
    'Position',[0.2 0.15 0.6 0.2]);
widths={'0.25' '0.5' '0.75' '1' '1.25' '1.5' '2' '2.5'};
for k=1:length(widths)
    tp.LineOptions.LineWidth.addItem(widths{k});
end
tp.LineOptions.LineWidth.setSelectedIndex(1);
addLabel(tp.LineOptions.LineWidth, 'Width');
tp.LineOptions.LineWidth.ActionPerformedCallback=@LineWidthCallback;
return
end

