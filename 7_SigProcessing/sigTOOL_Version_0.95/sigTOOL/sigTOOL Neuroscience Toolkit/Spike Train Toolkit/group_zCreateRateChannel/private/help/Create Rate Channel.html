<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Untitled Document</title>
<style type="text/css">
<!--
span.SpellE {mso-style-name:"";
	mso-spl-e:yes;}
div.MsoNormal {mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman";
	mso-fareast-font-family:"Times New Roman";}
li.MsoNormal {mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman";
	mso-fareast-font-family:"Times New Roman";}
p.MsoNormal {mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman";
	mso-fareast-font-family:"Times New Roman";}
.style1 {color: #808080}
.style2 {color: #000000}
-->
</style>
</head>

<body>
<div align="left">  
  <p align="right"><img src="099.jpg" width="80" height="40" alt="Logo" /></p>
  <h2>Create Rate Channel</h2>
  <p align="right" class="style1">Email: <EMAIL></p>
</div>
<hr />
<p align="right" class="style1">&nbsp;</p>
<p align="right" class="style1">Author: Malcolm Lidierth</p>
<p align="right" class="style1">Updated: June 2008 </p>
<h3 align="left" class="style2">Method</h3>
<p>You can convert a series of events such as spike to a waveform channel and apply the waveform analysis routines to the new channel.</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<h3>Options</h3>
<p align="center"><img src="011.jpg" alt="" width="492" height="203" /></p>
<p align="left">&nbsp;</p>
<h4 align="left">Channel Selection</h4>
<p align="left">Select the  channels  to convert in the Channel A selector. Select the channels to use for the output in the Channel B selector. You must select an equal number of source and target channels</p>
<h4 align="left">Start and Stop</h4>
<p align="left">Select the time range to use. </p>
<h4 align="left">Bin Width</h4>
<p align="left">Set the Bin Width for the output in milliseconds. This sets the effective sampling rate of the generated waveform channel [as 1/(Bin Width) Hz].</p>
<h4 align="left">Scaling</h4>
<p align="left">Allows you to select to count events (giving an output in units of events/bin) or to normalize the output to the a unit bin width of 1s, i.e. to output a rate in impulses/s.</p>
<h4 align="left">Smoothing</h4>
<p align="left">Digitally sampled signals are typically filtered prior to sampling using an anti-aliasing analogue filter with a high-frequency cut-off of less than half the sampling rate. The signal generated when a rate channel is created is not band-limited. Instantaneous changes occur in the data at the edges of the bins and this will give rise to components at all frequencies in the frequency domain. To limit this effect, the rate channel can be smoothed with a  filter. Three options are available: no filter, applying a rectangular window (a moving window average to the output) or filtering with a Gausian window (the default). For the rectangular and Guassian window, you need to select the window width. A preview of the effects of the filter (strictly on a band-limited signal) can be previewed using the View button: the example below shows the effects of an 11-point Gaussian window (the exact appearance of the preview will depend upon which Toolboxes, if any, that you have installed).</p>
<p align="center"><img src="012.jpg" alt="" width="681" height="417" /></p>
<p align="left">The effects of the filter on the power spectral density of a spike train are illustrated below. With no filter (top) and with an 11-point Gaussian window (lower figure).</p>
<p align="center"><img src="014.jpg" alt="" width="512" height="823" /></p>
<p>&nbsp;</p>
<p align="right">&copy; The Author and King's College London, 2008-</p>
</body>
</html>
