<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Untitled Document</title>
<style type="text/css">
<!--
span.SpellE {mso-style-name:"";
	mso-spl-e:yes;}
div.MsoNormal {mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman";
	mso-fareast-font-family:"Times New Roman";}
li.MsoNormal {mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman";
	mso-fareast-font-family:"Times New Roman";}
p.MsoNormal {mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman";
	mso-fareast-font-family:"Times New Roman";}
.style1 {color: #808080}
.style2 {color: #000000}
-->
</style>
</head>

<body>
<div align="left">  
  <p align="right"><img src="099.jpg" width="80" height="40" alt="Logo" /></p>
  <h2>Event crosscorrelation</h2>
  <p align="right" class="style1">Email: <EMAIL></p>
</div>
<hr />
<p align="right" class="style1">&nbsp;</p>
<p align="right" class="style1">Author: Malcolm Lidierth</p>
<p align="right" class="style1">Updated: June 2008 </p>
<h3 align="left" class="style2">Method</h3>
<p>Event crosscorrelation provides an estimate of the probabilty of observing an event as a function of time following a trigger event on the same or a different channel. By default, results are returned as events/sweeps (i.e. probability) but can subsequently be re-scaled to events/second (i.e rate).</p>
<p>&nbsp;</p>
<h3>Options</h3>
<p align="center"><img src="010.jpg" alt="" width="490" height="205" /></p>
<p align="left">&nbsp;</p>
<h4 align="left">Channel Selection</h4>
<p align="left">Select the trigger channels  in the Channel A selector and source channels using the Channel B selector. Any channels with multiple events can be selected.</p>
<h4 align="left">Duration &amp; Bin width</h4>
<p align="left">Sets the timebase for the resulting event crosscorrelations and the bin width to use (in seconds)</p>
<h4 align="left">Pre-time</h4>
<p align="left">Sets the pre-time as a percentage of the  duration.</p>
<h4 align="left">Sweeps per average</h4>
<p align="left">By default, all triggers will be used. To generate multiple correlations each with a fixed number of sweeps set Sweeps per Average to a number less than the total number of available triggers. The result will be presented as a 3-D histogram.</p>
<h4 align="left">Retrigger</h4>
<p align="left">The Retrigger checkbox is selected by default: triggers that fall during a preceding sweep will be used to trigger a subsequent sweep. </p>
<p align="left">Unchecking the Retrigger box will cause triggers that fall before the end of a preceding sweep to be ignored (this option is more typically used with post-event time histograms).</p>
<p align="left">&nbsp;</p>
<h3 align="left">Post-processing options</h3>
<p align="left">Right-click the mouse on the plotted result to display a context-senstive menu. Menu options include the ability to toggle between plotting events/sweep and event rate, to superimpose a cumulative sum (cusum) plot and to add a smoothed plot of the data (smoothed with a Gaussian with a width of w bins).</p>
<p align="center"><img src="002.jpg" alt="" width="370" height="263" /></p>
<p align="center">&nbsp;</p>
<p align="left">&nbsp;</p>
<p align="left">&nbsp;</p>
<p align="left">&nbsp;</p>
<p align="left">&nbsp;</p>
<h3>&nbsp;</h3>
<p align="right">&copy; The Author and King's College London, 2008-</p>
</body>
</html>
