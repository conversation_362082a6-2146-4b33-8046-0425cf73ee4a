<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Untitled Document</title>
<style type="text/css">
<!--
span.SpellE {mso-style-name:"";
	mso-spl-e:yes;}
div.MsoNormal {mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman";
	mso-fareast-font-family:"Times New Roman";}
li.MsoNormal {mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman";
	mso-fareast-font-family:"Times New Roman";}
p.MsoNormal {mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman";
	mso-fareast-font-family:"Times New Roman";}
.style1 {color: #808080}
.style2 {color: #000000}
-->
</style>
</head>

<body>
<div align="left">  
  <p align="right"><img src="099.jpg" width="80" height="40" alt="Logo" /></p>
  <h2>Raster</h2>
  <p align="right" class="style1">Email: <EMAIL></p>
</div>
<hr />
<p align="right" class="style1">&nbsp;</p>
<p align="right" class="style1">Author: Malcolm Lidierth</p>
<p align="right" class="style1">Updated: August 2008 </p>
<h3 align="left" class="style2">Method</h3>
<p>Displays events as a function of time following a trigger event on the same or a different channel. </p>
<p>&nbsp;</p>
<h3>Options</h3>
<p align="center">&nbsp;</p>
<p align="left">&nbsp;</p>
<h4 align="left">Channel Selection</h4>
<p align="left">Select the trigger channels  in the Channel A selector and source channels using the Channel B selector. Any channels with multiple events can be selected.</p>
<h4 align="left">Duration</h4>
<p align="left">Sets the timebase for the resulting correlation (in seconds)</p>
<h4 align="left">Pre-time</h4>
<p align="left">Sets the pre-time as a percentage of the  duration.</p>
<h4 align="left">Retrigger</h4>
<p align="left">The Retrigger checkbox is unselected by default: triggers that fall before the end of a preceding sweep will be ignored.</p>
<p align="left">Checking the Retrigger box will cause   triggers that fall during a preceding sweep to be used to trigger a subsequent sweep.</p>
<p align="left">&nbsp;</p>
<p align="right">&copy; The Author and King's College London, 2008-</p>
</body>
</html>
