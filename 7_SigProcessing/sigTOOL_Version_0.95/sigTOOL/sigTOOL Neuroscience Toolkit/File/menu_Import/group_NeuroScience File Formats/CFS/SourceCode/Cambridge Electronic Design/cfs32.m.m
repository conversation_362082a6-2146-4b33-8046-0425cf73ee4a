function [methodinfo,structs,enuminfo,ThunkLibName]=mfile
%MFILE Create structures to define interfaces found in 'Cfs'.

%This function was generated by loadlibrary.m parser version 1.1.6.24 on Tue Apr 15 22:59:23 2008
%perl options:'Cfs.i -outfile=mfile.m'
ival={cell(1,0)}; % change 0 to the actual number of functions to preallocate the data.
structs=[];enuminfo=[];fcnNum=1;
fcns=struct('name',ival,'calltype',ival,'LHS',ival,'RHS',ival,'alias',ival);
ThunkLibName=[];
% short WINAPI CreateCFSFile ( TpCStr fname , TpCStr comment , WORD blocksize , short channels , TpCVDesc fileArray , TpCVDesc DSArray , short fileVars , short DSVars ); 
fcns.name{fcnNum}='CreateCFSFile'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'cstring', 'cstring', 'uint16', 'int16', 'TVarDescPtr', 'TVarDescPtr', 'int16', 'int16'};fcnNum=fcnNum+1;
% void WINAPI SetFileChan ( short handle , short channel , TpCStr channelName , TpCStr yUnits , TpCStr xUnits , TDataType dataType , TCFSKind dataKind , short spacing , short other ); 
fcns.name{fcnNum}='SetFileChan'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'int16', 'cstring', 'cstring', 'cstring', 'int8', 'int8', 'int16', 'int16'};fcnNum=fcnNum+1;
% void WINAPI SetDSChan ( short handle , short channel , WORD dataSection , long startOffset , long points , float yScale , float yOffset , float xScale , float xOffset ); 
fcns.name{fcnNum}='SetDSChan'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'int16', 'uint16', 'long', 'long', 'single', 'single', 'single', 'single'};fcnNum=fcnNum+1;
% short WINAPI WriteData ( short handle , WORD dataSection , long startOffset , WORD bytes , TpVoid dataADS ); 
fcns.name{fcnNum}='WriteData'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'int16', 'uint16', 'long', 'uint16', 'voidPtr'};fcnNum=fcnNum+1;
% short WINAPI ClearDS ( short handle ); 
fcns.name{fcnNum}='ClearDS'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'int16'};fcnNum=fcnNum+1;
% void WINAPI SetWriteData ( short handle , long startOffset , long bytes ); 
fcns.name{fcnNum}='SetWriteData'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'long', 'long'};fcnNum=fcnNum+1;
% long WINAPI CFSFileSize ( short handle ); 
fcns.name{fcnNum}='CFSFileSize'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='long'; fcns.RHS{fcnNum}={'int16'};fcnNum=fcnNum+1;
% short WINAPI InsertDS ( short handle , WORD dataSection , TSFlags flagSet ); 
fcns.name{fcnNum}='InsertDS'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'int16', 'uint16', 'uint16'};fcnNum=fcnNum+1;
% short WINAPI AppendDS ( short handle , long lSize , TSFlags flagSet ); 
fcns.name{fcnNum}='AppendDS'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'int16', 'long', 'uint16'};fcnNum=fcnNum+1;
% void WINAPI RemoveDS ( short handle , WORD dataSection ); 
fcns.name{fcnNum}='RemoveDS'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'uint16'};fcnNum=fcnNum+1;
% void WINAPI SetComment ( short handle , TpCStr comment ); 
fcns.name{fcnNum}='SetComment'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'cstring'};fcnNum=fcnNum+1;
% void WINAPI SetVarVal ( short handle , short varNo , short varKind , WORD dataSection , TpVoid varADS ); 
fcns.name{fcnNum}='SetVarVal'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'int16', 'int16', 'uint16', 'voidPtr'};fcnNum=fcnNum+1;
% short WINAPI CloseCFSFile ( short handle ); 
fcns.name{fcnNum}='CloseCFSFile'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'int16'};fcnNum=fcnNum+1;
% short WINAPI OpenCFSFile ( TpCStr fname , short enableWrite , short memoryTable ); 
fcns.name{fcnNum}='OpenCFSFile'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'cstring', 'int16', 'int16'};fcnNum=fcnNum+1;
% void WINAPI GetGenInfo ( short handle , TpStr time , TpStr date , TpStr comment ); 
fcns.name{fcnNum}='GetGenInfo'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'cstring', 'cstring', 'cstring'};fcnNum=fcnNum+1;
% void WINAPI GetFileInfo ( short handle , TpShort channels , TpShort fileVars , TpShort DSVars , TpUShort dataSections ); 
fcns.name{fcnNum}='GetFileInfo'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'int16Ptr', 'int16Ptr', 'int16Ptr', 'uint16Ptr'};fcnNum=fcnNum+1;
% void WINAPI GetVarDesc ( short handle , short varNo , short varKind , TpShort varSize , TpDType varType , TpStr units , TpStr description ); 
fcns.name{fcnNum}='GetVarDesc'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'int16', 'int16', 'int16Ptr', 'int8Ptr', 'cstring', 'cstring'};fcnNum=fcnNum+1;
% void WINAPI GetVarVal ( short handle , short varNo , short varKind , WORD dataSection , TpVoid varADS ); 
fcns.name{fcnNum}='GetVarVal'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'int16', 'int16', 'uint16', 'voidPtr'};fcnNum=fcnNum+1;
% void WINAPI GetFileChan ( short handle , short channel , TpStr channelName , TpStr yUnits , TpStr xUnits , TpDType dataType , TpDKind dataKind , TpShort spacing , TpShort other ); 
fcns.name{fcnNum}='GetFileChan'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'int16', 'cstring', 'cstring', 'cstring', 'int8Ptr', 'int8Ptr', 'int16Ptr', 'int16Ptr'};fcnNum=fcnNum+1;
% void WINAPI GetDSChan ( short handle , short channel , WORD dataSection , TpLong startOffset , TpLong points , TpFloat yScale , TpFloat yOffset , TpFloat xScale , TpFloat xOffset ); 
fcns.name{fcnNum}='GetDSChan'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'int16', 'uint16', 'longPtr', 'longPtr', 'singlePtr', 'singlePtr', 'singlePtr', 'singlePtr'};fcnNum=fcnNum+1;
% WORD WINAPI GetChanData ( short handle , short channel , WORD dataSection , long firstElement , WORD numberElements , TpVoid dataADS , long areaSize ); 
fcns.name{fcnNum}='GetChanData'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='uint16'; fcns.RHS{fcnNum}={'int16', 'int16', 'uint16', 'long', 'uint16', 'voidPtr', 'long'};fcnNum=fcnNum+1;
% long WINAPI GetDSSize ( short handle , WORD dataSection ); 
fcns.name{fcnNum}='GetDSSize'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='long'; fcns.RHS{fcnNum}={'int16', 'uint16'};fcnNum=fcnNum+1;
% short WINAPI ReadData ( short handle , WORD dataSection , long startOffest , WORD bytes , TpVoid dataADS ); 
fcns.name{fcnNum}='ReadData'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'int16', 'uint16', 'long', 'uint16', 'voidPtr'};fcnNum=fcnNum+1;
% WORD WINAPI DSFlagValue ( int nflag ); 
fcns.name{fcnNum}='DSFlagValue'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='uint16'; fcns.RHS{fcnNum}={'int32'};fcnNum=fcnNum+1;
% void WINAPI DSFlags ( short handle , WORD dataSection , short setIt , TpFlags pflagSet ); 
fcns.name{fcnNum}='DSFlags'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}=[]; fcns.RHS{fcnNum}={'int16', 'uint16', 'int16', 'uint16Ptr'};fcnNum=fcnNum+1;
% short WINAPI FileError ( TpShort handleNo , TpShort procNo , TpShort errNo ); 
fcns.name{fcnNum}='FileError'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'int16Ptr', 'int16Ptr', 'int16Ptr'};fcnNum=fcnNum+1;
% short WINAPI CommitCFSFile ( short handle ); 
fcns.name{fcnNum}='CommitCFSFile'; fcns.calltype{fcnNum}='stdcall'; fcns.LHS{fcnNum}='int16'; fcns.RHS{fcnNum}={'int16'};fcnNum=fcnNum+1;
structs.TVarDesc.packing=1;
structs.TVarDesc.members=struct('varDesc', 'int8#22', 'vType', 'int8', 'zeroByte', 'int8', 'varUnits', 'int8#10', 'vSize', 'int16');
methodinfo=fcns;