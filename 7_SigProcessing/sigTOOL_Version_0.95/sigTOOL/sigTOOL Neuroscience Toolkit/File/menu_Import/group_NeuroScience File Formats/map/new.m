function [methodinfo,structs,enuminfo,ThunkLibName]=new
%NEW Create structures to define interfaces found in 'ns'.

%This function was generated by loadlibrary.m parser version 1.1.6.29 on Wed Sep 30 00:18:34 2009
%perl options:'ns.i -outfile=new.m'
ival={cell(1,0)}; % change 0 to the actual number of functions to preallocate the data.
structs=[];enuminfo=[];fcnNum=1;
fcns=struct('name',ival,'calltype',ival,'LHS',ival,'RHS',ival,'alias',ival);
ThunkLibName=[];
% ns_DLLHANDLE ns_LoadLibrary ( const char * libname ); 
fcns.name{fcnNum}='ns_LoadLibrary'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'cstring'};fcnNum=fcnNum+1;
% ns_RESULT ns_CloseLibrary ( ns_DLLHANDLE nsDllHandle ); 
fcns.name{fcnNum}='ns_CloseLibrary'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetLibraryInfo ( ns_DLLHANDLE nssDllHANDLE , ns_LIBRARYINFO * pLibraryInfo , UINT32 dwLibraryInfoSize ); 
fcns.name{fcnNum}='ns_GetLibraryInfo'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'GCC_ALIGN_4Ptr', 'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_OpenFile ( ns_DLLHANDLE nssDllHANDLE , const char * pszFilename , UINT32 * hFile ); 
fcns.name{fcnNum}='ns_OpenFile'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'cstring', 'voidPtr'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetFileInfo ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , ns_FILEINFO * pFileInfo , UINT32 dwFileInfoSize ); 
fcns.name{fcnNum}='ns_GetFileInfo'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'GCC_ALIGN_4Ptr', 'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_CloseFile ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile ); 
fcns.name{fcnNum}='ns_CloseFile'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetEntityInfo ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , ns_ENTITYINFO * pEntityInfo , UINT32 dwEntityInfoSize ); 
fcns.name{fcnNum}='ns_GetEntityInfo'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'ns_ENTITYINFOPtr', 'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetEventInfo ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , ns_EVENTINFO * pEventInfo , UINT32 dwEventInfoSize ); 
fcns.name{fcnNum}='ns_GetEventInfo'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'GCC_ALIGN_4Ptr', 'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetEventData ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , UINT32 nIndex , double * pdTimeStamp , void * pData , UINT32 dwDataSize , UINT32 * pdwDataRetSize ); 
fcns.name{fcnNum}='ns_GetEventData'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'error', 'doublePtr', 'voidPtr', 'error', 'voidPtr'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetAnalogInfo ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , ns_ANALOGINFO * pAnalogInfo , UINT32 dwAnalogInfoSize ); 
fcns.name{fcnNum}='ns_GetAnalogInfo'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'GCC_ALIGN_4Ptr', 'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetAnalogData ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , UINT32 dwStartIndex , UINT32 dwIndexCount , UINT32 * pdwContCount , double * pData ); 
fcns.name{fcnNum}='ns_GetAnalogData'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'error', 'error', 'voidPtr', 'doublePtr'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetSegmentInfo ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , ns_SEGMENTINFO * pSegmentInfo , UINT32 dwSegmentInfoSize ); 
fcns.name{fcnNum}='ns_GetSegmentInfo'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'GCC_ALIGN_4Ptr', 'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetSegmentSourceInfo ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , UINT32 dwSourceID , ns_SEGSOURCEINFO * pSourceInfo , UINT32 dwSourceInfoSize ); 
fcns.name{fcnNum}='ns_GetSegmentSourceInfo'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'error', 'GCC_ALIGN_4Ptr', 'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetSegmentData ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , UINT32 nIndex , double * pdTimeStamp , double * pdData , UINT32 dwDataBufferSize , UINT32 * pdwSampleCount , UINT32 * pdwUnitID ); 
fcns.name{fcnNum}='ns_GetSegmentData'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'error', 'doublePtr', 'doublePtr', 'error', 'voidPtr', 'voidPtr'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetNeuralInfo ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , ns_NEURALINFO * pNeuralInfo , UINT32 dwNeuralInfoSize ); 
fcns.name{fcnNum}='ns_GetNeuralInfo'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'GCC_ALIGN_4Ptr', 'error'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetNeuralData ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , UINT32 dwStartIndex , UINT32 dwIndexCount , double * pdData ); 
fcns.name{fcnNum}='ns_GetNeuralData'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'error', 'error', 'doublePtr'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetIndexByTime ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , double dTime , INT32 nFlag , UINT32 * pdwIndex ); 
fcns.name{fcnNum}='ns_GetIndexByTime'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'double', 'error', 'voidPtr'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetTimeByIndex ( ns_DLLHANDLE nssDllHANDLE , UINT32 hFile , UINT32 dwEntityID , UINT32 dwIndex , double * pdTime ); 
fcns.name{fcnNum}='ns_GetTimeByIndex'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'error', 'error', 'error', 'doublePtr'};fcnNum=fcnNum+1;
% ns_RESULT ns_GetLastErrorMsg ( ns_DLLHANDLE nssDllHANDLE , char * pszMsgBuffer , UINT32 dwMsgBufferSize ); 
fcns.name{fcnNum}='ns_GetLastErrorMsg'; fcns.calltype{fcnNum}='cdecl'; fcns.LHS{fcnNum}='error'; fcns.RHS{fcnNum}={'error', 'cstring', 'error'};fcnNum=fcnNum+1;
structs.GCC_ALIGN_4.members=struct('dwSourceEntityID', 'error', 'dwSourceUnitID', 'error', 'szProbeInfo', 'int8#128');
structs.GCC_ALIGN_4.members=struct('dwSourceEntityID', 'error', 'dwSourceUnitID', 'error', 'szProbeInfo', 'int8#128');
structs.GCC_ALIGN_4.members=struct('dwSourceEntityID', 'error', 'dwSourceUnitID', 'error', 'szProbeInfo', 'int8#128');
structs.ns_ENTITYINFO.members=struct('szEntityLabel', 'int8#32', 'dwEntityType', 'error', 'dwItemCount', 'error');
structs.GCC_ALIGN_4.members=struct('dwSourceEntityID', 'error', 'dwSourceUnitID', 'error', 'szProbeInfo', 'int8#128');
structs.GCC_ALIGN_4.members=struct('dwSourceEntityID', 'error', 'dwSourceUnitID', 'error', 'szProbeInfo', 'int8#128');
structs.GCC_ALIGN_4.members=struct('dwSourceEntityID', 'error', 'dwSourceUnitID', 'error', 'szProbeInfo', 'int8#128');
structs.GCC_ALIGN_4.members=struct('dwSourceEntityID', 'error', 'dwSourceUnitID', 'error', 'szProbeInfo', 'int8#128');
structs.GCC_ALIGN_4.members=struct('dwSourceEntityID', 'error', 'dwSourceUnitID', 'error', 'szProbeInfo', 'int8#128');
enuminfo.ns_ENTITY_TYPE=struct('ns_ENTITY_UNKNOWN',0,'ns_ENTITY_EVENT',1,'ns_ENTITY_ANALOG',2,'ns_ENTITY_SEGMENT',3,'ns_ENTITY_NEURALEVENT',4);
enuminfo.ns_EVENT_TYPE=struct('ns_EVENT_TEXT',0,'ns_EVENT_CSV',1,'ns_EVENT_BYTE',2,'ns_EVENT_WORD',3,'ns_EVENT_DWORD',4);
enuminfo.ns_LIBRARY_FLAGS=struct('ns_LIBRARY_DEBUG',1,'ns_LIBRARY_MODIFIED',2,'ns_LIBRARY_PRERELEASE',4,'ns_LIBRARY_SPECIALBUILD',8,'ns_LIBRARY_MULTITHREADED',16);
enuminfo.ns_STATUS=struct('ns_OK',0,'ns_LIBERROR',-1,'ns_TYPEERROR',-2,'ns_FILEERROR',-3,'ns_BADFILE',-4,'ns_BADENTITY',-5,'ns_BADSOURCE',-6,'ns_BADINDEX',-7);
enuminfo.ns_LOCATION_FLAGS=struct('ns_BEFORE',-1,'ns_CLOSEST',0,'ns_AFTER',1);
methodinfo=fcns;