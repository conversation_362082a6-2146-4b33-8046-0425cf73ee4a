These files provide a gateway to the functions in the Molecular Devices (Axon Instruments)
Windows application extension for ABF files (abffio.dll)

To compile these files just run

mex filename.cpp

for each cpp file at the MATLAB prompt.


In sigTOOL:

The resulting mex files should be copied to the folder immediately above .../SourceCode
in the folder hierarchy.


N.B. If errors are shown, check that the required h-files are located as specified on the path.




ML
08/07

Note
08/08 Recompiled with Borland C++ on R2006a for sigTOOL distribution
10/08 Memory overflow errors in Comment fixed
12/08 Recompiled with Microsoft Visual Studio 2005 on R2006a for sigTOOL distribution
