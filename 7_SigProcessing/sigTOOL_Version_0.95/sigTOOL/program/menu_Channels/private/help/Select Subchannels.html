<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Untitled Document</title>
<style type="text/css">
<!--
.style1 {color: #808080}
.style2 {color: #000000; }
-->
</style>
</head>

<body>
<div align="left">
  <p align="right"><img src="../../../Logo.gif" width="80" height="40" alt="Logo" /></p>
  <h2>Select Subchannel</h2>
  <p align="right" class="style1">Email: <EMAIL></p>
</div>
<hr />
<p align="right" class="style1">&nbsp;</p>
<p align="right" class="style1">Author: <PERSON></p>
<p align="right" class="style1">Updated: February 2008</p>
<p align="left" class="style2">When a file contains multiplexed waveform channels, you can select which subchannel to analyze from each channel using the Channel-&gt;Select Subchannels menu. This allows the current subchannel setting for each channel to be changed. By default, the current subchannel is 1.</p>
<p align="left" class="style1">&nbsp;</p>
<p align="center" class="style1"><img src="Subchannel Selection" alt="Subchannel Selection" /></p>
<p align="left" class="style1"><span class="style2">All relevant commands from the Channel menu (Decimate, Digital Filter, Copy To...) respect the current subchannel setting. Those in the Waveform menu may or may not. You can still apply  analysis routines that do not support multiplexed data to each subchannel, but you must first extract the relevant subchannel using the Channel -&gt; Copy To Temporary Channel command. This copies the data from the current subchannel of the selected channel to a new, non-multiplexed, channel.</span></p>
<p align="left" class="style2">By default, subchannel 1 is set as the current subchannel. You can alter this setting by selecting from the list of multiplexed channel and setting a new value in the Subchannel field.</p>
<p align="left" class="style2">Reset All returns the current subchannel to 1 on all multiplexed channels.</p>
<p align="left" class="style2">'Apply to all open files' is not activated in this menu.</p>
<p align="left" class="style2">&nbsp;</p>
<p align="left" class="style2">&nbsp;</p>
</body>
</html>
