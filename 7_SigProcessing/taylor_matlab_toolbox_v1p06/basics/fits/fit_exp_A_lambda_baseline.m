function [A,lambda,baseline,mse] = f(A0,lambda0,baseline0,x,y,f_tol)

% fit each to an exponential
theta0=[A0 lambda0 baseline0]';
theta_lb=[-Inf  eps -Inf]';
theta_ub=[+Inf +Inf +Inf]';
options=optimset('LargeScale','off',...
                 'MaxFunEvals',1e6,...
                 'MaxIter',1e4,...
                 'TolFun',f_tol,...
                 'TolX',0);
[theta,mse,exitflag,output,lambda_other,grad,Hessian]=...
  fmincon(@exp_A_lambda_baseline_mse,theta0,...
          [],[],[],[],...
          theta_lb,theta_ub,...
          [],...
          options,...
          x,y);
A=theta(1);
lambda=theta(2);
baseline=theta(3);
