1.00 -> 1.01

Added hds/ directory, the "Hessian direction set" optimization routine.  
Note that it now has a switch so that you can do fixed direction set, 
<PERSON>'s method, or my "Hessian direction set" method.

Added help for the function event_triggered().

Deleted event_triggered_double(), since one can just call 
event_triggered() twice.

Deleted fig_triggered_average(), redundant.

Renamed four functions with names like fig_spike_triggered_whatever() to 
fig_event_triggered_whatever, for consistency.

Changed hist_from_data() to more recent version.

Deleted histc_from_data(), so hist_from_data() and histc_from_data() 
weren't out-of-register.  Should probably rewrite histc_from_data() to
be parallel to new hist_from_data().



1.01 -> 1.02

Removed cov(), because there's a cov() function built into Matlab that 
does the same thing.  How did I miss that?

Added bipolar(), a colormap function.

Changed to using newer version (2.31) of MATLAB SON Library.  Changed my 
functions that do SON stuff to be compatible.  <PERSON> did the
modification of load_smr().

Changed to making the user download MATLAB SON Library themselves, because 
<PERSON> asked us to.



1.02 -> 1.03

Moved testing code out of the library, so that adding the library to path
doesn't add any 'modpath.m' files to the path.

Deleted analyze_*.m functions, Z_all_sweeps() from 
data_analysis/impedance_fitting.  Those functions are really not of general 
utility.

Added get_home_dir_name() function.



1.03 -> 1.04

Added stg_spectra directory under data_analysis.  This includes code for 
making spectrograms and cohereograms in a way that's tailored for use
with STG rhythms.  Some functions also added in other places to support this.
The code for the plots is similar to what we used for Kris' paper, except we
no longer do the gaussian filtering before calculating the spectra.



1.04 -> 1.05

Added function for plotting classic phase diagrams.
Added functions for setting ylims on all axes in a figure.
Added functions mean_circular() and std_circular, for calculating the
mean and standard deviation of angles.
Added function set_axes_size().
Changed print_pdf() to use ghostscript, and to work on UNIX.
Changed print_png() to work on UNIX.
Changed print_tiff() to work on UNIX.
Fixed bug in save_tabular_data() -- didn't open file in text mode.



1.05 -> 1.06

Changed some of the spectrogram and coherogram plotting functions to
be more rational.

