function return_value = f(data,fps)

% This is vcr, a function for viewing color movies.  The data arg should be
% of shape [n_rows n_cols 3 n_frames], with either double or uint8
% elements.  If data is doubles, they get converted to uint8s (using the
% min-max method) and the original data is not stored in the figure.

% set defaults
frame_index=1;
if (nargin<2)
  fps=20;  % Frames per second
end

% get dims, make sure data is 4D
data_var_name = inputname(1);
dims=length(size(data));
if (dims==3)
  n_frames=1;
elseif (dims==4)
  n_frames=size(data,4);
else
  error('data must have four or less dimensions');
end
n_channels=size(data,3);
if n_channels~=3
  error('data must be an RGB image or movie');
end
n_rows=size(data,1);
n_cols=size(data,2);
data=reshape(data,[n_rows,n_cols,3,n_frames]);
data_min=min(data(:));
data_max=max(data(:));
if isa(data,'uint8')
  indexed_data=data;
else
  indexed_data=uint8(round(255*(data-data_min)/(data_max-data_min));
end
this_frame=indexed_data(:,:,:,frame_index);

% get the screen size so we can position the figure window
root_units=get(0,'Units');
set(0,'Units','pixels');
screen_dims=get(0,'ScreenSize');
screen_width=screen_dims(3);
screen_height=screen_dims(4);
set(0,'Units',root_units);

% spec out the figure layout
image_area_width=1024;
image_area_height=1024;
right_pad_width=15;
left_pad_width=15;
top_pad_height=40;
bottom_pad_height=50;
n_vcr_buttons=7;
vcr_button_width=30;
vcr_button_height=20;
vcr_button_spacer_width=image_area_width/n_vcr_buttons-vcr_button_width;
right_panel_spacer_height=7;
figure_width=image_area_width+right_pad_width+left_pad_width;
figure_height=image_area_height+top_pad_height+bottom_pad_height;

% determine the default zoom factor.  It should be the largest integer
% s.t. the image still fits within the image area
zoom=min(floor(image_area_width/n_cols),floor(image_area_height/n_rows));

% create the figure and main axes for the image
figure_h = figure('Tag','figure_h',...
                  'Position',[100,screen_height-figure_height-150+1,...
                              figure_width,figure_height],...
                  'Name',sprintf('%s',data_var_name),...
                  'NumberTitle','off',...
                  'KeyPressFcn','vcr_callback(''key'')',...
                  'Resize','off');
axes_position=[left_pad_width+(image_area_width-zoom*n_cols)/2,...
               bottom_pad_height+(image_area_height-zoom*n_rows)/2,...
               zoom*n_cols,...
               zoom*n_rows];             
axes_h = axes('Parent',figure_h,...
              'Tag','axes_h',...
              'YDir','reverse',...
              'DrawMode','fast',...
              'Visible','off',...
              'Units','pixels',...
              'Position',axes_position,...
              'XLim',[0.5,n_cols+0.5],...
              'YLim',[0.5,n_rows+0.5],...
              'DataAspectRatio',[1 1 1]);
image_h = image('Parent',axes_h,...
                'CData',this_frame,...
                'Tag','image_h',...
                'EraseMode','none');

% VCR-style controls
to_start_button_h = ...
  uicontrol('Parent',figure_h,...
            'Style','pushbutton',...
            'String','|<',...
            'Tag','to_start_button_h',...
            'Position',...
              [left_pad_width+vcr_button_spacer_width/2+...
                 (1-1)*(vcr_button_width+vcr_button_spacer_width),...
               (bottom_pad_height-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','vcr_callback');
play_backward_button_h = ...
  uicontrol('Parent',figure_h,...
            'Style','pushbutton',...
            'String','<',...
            'Tag','play_backward_button_h',...
            'Position',...
              [left_pad_width+vcr_button_spacer_width/2+...
                 (2-1)*(vcr_button_width+vcr_button_spacer_width),...
               (bottom_pad_height-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','vcr_callback');
frame_backward_button_h = ...
  uicontrol('Parent',figure_h,...
            'Style','pushbutton',...
            'String','<|',...
            'Tag','frame_backward_button_h',...
            'Position',...
              [left_pad_width+vcr_button_spacer_width/2+...
                 (3-1)*(vcr_button_width+vcr_button_spacer_width),...
               (bottom_pad_height-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','vcr_callback');
stop_button_h = ...
  uicontrol('Parent',figure_h,...
            'Style','pushbutton',...
            'String','O',...
            'Tag','stop_button_h',...
            'Position',...
              [left_pad_width+vcr_button_spacer_width/2+...
                 (4-1)*(vcr_button_width+vcr_button_spacer_width),...
               (bottom_pad_height-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','vcr_callback');
frame_forward_button_h = ...
  uicontrol('Parent',figure_h,...
            'Style','pushbutton',...
            'String','|>',...
            'Tag','frame_forward_button_h',...
            'Position',...
              [left_pad_width+vcr_button_spacer_width/2+...
                 (5-1)*(vcr_button_width+vcr_button_spacer_width),...
               (bottom_pad_height-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','vcr_callback');
play_forward_button_h = ...
  uicontrol('Parent',figure_h,...
            'Style','pushbutton',...
            'String','>',...
            'Tag','play_forward_button_h',...
            'Position',...
              [left_pad_width+vcr_button_spacer_width/2+...
                 (6-1)*(vcr_button_width+vcr_button_spacer_width),...
               (bottom_pad_height-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','vcr_callback');
to_end_button_h = ...
  uicontrol('Parent',figure_h,...
            'Style','pushbutton',...
            'String','>|',...
            'Tag','to_end_button_h',...
            'Position',...
              [left_pad_width+vcr_button_spacer_width/2+...
                 (7-1)*(vcr_button_width+vcr_button_spacer_width),...
               (bottom_pad_height-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','vcr_callback');

% Frame index counter
frame_text_h = ...
  uicontrol('Parent',figure_h,...
            'Style','text',...
            'String','Frame ',...
            'Tag','frame_text_h',...
            'Callback','vcr_callback',...
            'BackgroundColor',get(figure_h,'Color'));
extent=get(frame_text_h,'Extent');
frame_text_width=extent(3);
frame_text_height=extent(4);
frame_index_edit_h = ...
  uicontrol('Parent',figure_h,...
            'Style','edit',...
            'String',sprintf('%d',n_frames),...
            'Tag','frame_index_edit_h',...
            'Min',0,...
            'Max',1,...
            'HorizontalAlignment','right',...
            'BackgroundColor','white',...
            'Callback','vcr_callback');
extent=get(frame_index_edit_h,'Extent');
frame_index_edit_width=extent(3)+10;  % need padding
frame_index_edit_height=extent(4)+2;  % need padding
set(frame_index_edit_h,...
    'String',sprintf('%d',frame_index));
of_n_frames_text_h = ...
  uicontrol('Parent',figure_h,...
            'Style','text',...
            'String',sprintf(' of %d',n_frames),...
            'Tag','of_n_frames_text_h',...
            'Callback','vcr_callback',...
            'BackgroundColor',get(figure_h,'Color'));
extent=get(of_n_frames_text_h,'Extent');
of_n_frames_text_width=extent(3);
of_n_frames_text_height=extent(4);
% set positions of frame counter elements
full_frame_text_width=frame_text_width+...
                      frame_index_edit_width+...
                      of_n_frames_text_width;
frame_text_left_margin=left_pad_width;
frame_text_baseline=figure_height-(top_pad_height+frame_text_height)/2;
set(frame_text_h,'Position',...
                 [frame_text_left_margin,...
                  frame_text_baseline,...
                  frame_text_width,...
                  frame_text_height]);
set(frame_index_edit_h,...
    'Position',[frame_text_left_margin+frame_text_width,...
                frame_text_baseline+...
                  (frame_text_height-frame_index_edit_height)/2+2,...
                frame_index_edit_width,...
                frame_index_edit_height]);
set(of_n_frames_text_h,'Position',...
                       [frame_text_left_margin+frame_text_width+...
                          frame_index_edit_width,...
                        frame_text_baseline,...
                        of_n_frames_text_width,...
                        of_n_frames_text_height]);

% Frames per second controls
FPS_text_h = ...
  uicontrol('Parent',figure_h,...
            'Style','text',...
            'String','FPS: ',...
            'Tag','FPS_text_h',...
            'Callback','vcr_callback',...
            'BackgroundColor',get(figure_h,'Color'));
extent=get(FPS_text_h,'Extent');
FPS_text_width=extent(3);
FPS_text_height=extent(4);
FPS_edit_h = ...
  uicontrol('Parent',figure_h,...
            'Style','edit',...
            'String',sprintf('%6.2f',fps),...
            'Tag','FPS_edit_h',...
            'Min',0,...
            'Max',1,...
            'HorizontalAlignment','right',...
            'BackgroundColor','white',...
            'Callback','vcr_callback');
extent=get(FPS_edit_h,'Extent');
FPS_edit_width=extent(3)+10;  % padding
FPS_edit_height=extent(4)+2;  % padding
% set positions of FPS elements
FPS_elements_width=FPS_text_width+FPS_edit_width;
FPS_elements_left_margin=left_pad_width+image_area_width-...
                         FPS_elements_width;
FPS_elements_baseline=figure_height-(top_pad_height+FPS_text_height)/2;
set(FPS_text_h,'Position',...
               [FPS_elements_left_margin,...
                FPS_elements_baseline,...
                FPS_text_width,...
                FPS_text_height]);
set(FPS_edit_h,...
    'Position',[FPS_elements_left_margin+FPS_text_width,...
                FPS_elements_baseline+...
                  (FPS_text_height-FPS_edit_height)/2+2,...
                FPS_edit_width,...
                FPS_edit_height]);

% set up the figure state variables         
set_userdata(figure_h,'data_var_name',data_var_name);
set_userdata(figure_h,'frame_index',frame_index);
set_userdata(figure_h,'fps',fps);
set_userdata(figure_h,'stop_button_hit',0);
% this is a kludge, but we'll store these guys somewhere other than the
% figure's UserData, since (by using get_userdata and set_userdata) that's a
% structure that has to be copied out and copied back everytime a field of
% it is modified
set(axes_h,'UserData',indexed_data);


% add some menus
%view_menu_h=uimenu(figure_h,...
%                   'Tag','view_menu_h',...
%                   'Label','View');
%copy_as_bitmap_menu_h=uimenu(view_menu_h,...
%                             'Label','Copy as Bitmap',...
%                             'Tag','copy_as_bitmap_menu_h',...
%                             'Callback','vcr_callback');

% return figure handle if needed
if nargout > 0
  return_value = figure_h; 
end
        



