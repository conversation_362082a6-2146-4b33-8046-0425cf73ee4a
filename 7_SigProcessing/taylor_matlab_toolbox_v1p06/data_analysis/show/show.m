function f(t_o,data,t_e,e_phys)

% this is show, a procedure for viewing data movies, and for extracting
% time signals from ROIs in those movies.  The data should be of shape
% [n_rows n_cols n_frames].  The elements can be pretty much any type, but
% are converted to doubles before anything is done to them.  The figure
% carries around both a double version of the data and an indexed version.
%
% t_o, t_e should be col vectors, and in units of msec

% process args
if nargin<3
  t_e=[];
end
if nargin<4
  e_phys=[];
end

% set defaults
frame_index=1;
mode='elliptic_roi';
cmap_name='gray';

% get dims, make sure data is 3D
dims=ndims(data);
if (dims>3)
  error('data must have three or less dimensions');
end
n_rows=size(data,1);
n_cols=size(data,2);
n_frames=length(t_o);
n_samples=length(t_e);
data=double(reshape(data,[n_rows,n_cols,n_frames]));

% get the screen size so we can position the figure window
root_units=get(0,'Units');
set(0,'Units','pixels');
screen_dims=get(0,'ScreenSize');
screen_width=screen_dims(3);
screen_height=screen_dims(4);
set(0,'Units',root_units);

%
% spec out the layout of the figure
%

% layout of the fig numbers
screen_left_pad_size=20;
screen_top_pad_size=50;

% the figure
biggest_power_of_two_that_fits=2^floor(log2(min(screen_width,screen_height)));
image_area_width=biggest_power_of_two_that_fits;
image_area_height=biggest_power_of_two_that_fits;

n_mode_buttons=6;
mode_button_width=70;
mode_button_height=30;
mode_button_spacer_height=0;

n_action_buttons=2;
action_button_width=mode_button_width;
action_button_height=mode_button_height;
action_button_spacer_height=0;

button_image_pad_width=20;  % pad between mode/action buttons and image 
colorbar_area_width=30;
colorbar_area_height=image_area_height;
image_colorbar_pad_width=50;  % pad between image area and colorbar
n_vcr_buttons=7;
vcr_button_width=60;
vcr_button_height=20;
vcr_button_spacer_width=image_area_width/n_vcr_buttons-vcr_button_width;
figure_right_pad_size=20;
figure_left_pad_size=20;
figure_top_pad_size=40;
figure_bottom_pad_size=50;
figure_width=figure_left_pad_size+...
           mode_button_width+...
           button_image_pad_width+...
           image_area_width+...
           image_colorbar_pad_width+...
           colorbar_area_width+...
           figure_right_pad_size;
figure_height=figure_top_pad_size+image_area_height+figure_bottom_pad_size;

% determine the default zoom factor.  It should be the largest integer
% s.t. the image still fits within the image area
zoom=min(floor(image_area_width/n_cols),floor(image_area_height/n_rows));

%
% Image figure and children
%
                      
% create the image figure
posit=[screen_left_pad_size,...
       screen_height-figure_height-screen_top_pad_size+1,...
       figure_width,figure_height];
show_figure_h = ...
  figure('Tag','show_figure_h',...
         'Position',posit,...
         'Name','Show',...
         'NumberTitle','off',...
         'KeyPressFcn','show_handle_key',...
         'Colormap',eval(sprintf('%s(256)',cmap_name)),...
         'Resize','off',...
         'MenuBar','none',...
         'Color',[236 233 216]/255,...
         'PaperPositionMode','auto',...
         'InvertHardcopy','off',...
         'DoubleBuffer','on');

% have to do this last, otherwise having the pointer in the place
% where the window appears causes an error at startup
%        'WindowButtonMotionFcn','show_update_pointer');

%plotedit(show_figure_h,'hidetoolsmenu');

% figure out the colorbar min and colorbar max
data_min=min(data(:));
data_max=max(data(:));
colorbar_min_string=sprintf('%.4e',data_min);
colorbar_max_string=sprintf('%.4e',data_max);
colorbar_min=str2num(colorbar_min_string);
colorbar_max=str2num(colorbar_max_string);

% create the colorbar axes and the colorbar image
colorbar_axes_position=[figure_left_pad_size+...
                          mode_button_width+...
                          button_image_pad_width+...
                          image_area_width+...
                          image_colorbar_pad_width,...
                        figure_bottom_pad_size,...
                        colorbar_area_width,...
                        colorbar_area_height];
colorbar_increment=(colorbar_max-colorbar_min)/256;
colorbar_axes_h = axes('Parent',show_figure_h,...
                       'Tag','colorbar_axes_h',...
                       'Units','pixels',...
                       'Position',colorbar_axes_position,...
                       'Visible','on',...
                       'Box','on',...
                       'XLim',[0.5 1.5],...
                       'YLim',[colorbar_min colorbar_max],...
                       'XTick',[],...
                       'Layer','top');
colorbar_h = image('Parent',colorbar_axes_h,...
                   'CData',(0:255)',...
                   'Tag','colorbar_h',...
                   'XData',[1 1],...
                   'YData',[colorbar_min+0.5*colorbar_increment...
                            colorbar_max-0.5*colorbar_increment],...
                   'ButtonDownFcn','show_callback');

% create the image axes and the image
indexed_data=uint8(round(255*(data-colorbar_min)/...
                             (colorbar_max-colorbar_min)));
this_frame=indexed_data(:,:,frame_index);
image_axes_position=...
  [figure_left_pad_size+mode_button_width+button_image_pad_width+...
     (image_area_width-zoom*n_cols)/2,...
   figure_bottom_pad_size+(image_area_height-zoom*n_rows)/2,...
   zoom*n_cols,...
   zoom*n_rows];             
image_axes_h = axes('Parent',show_figure_h,...
                    'Tag','image_axes_h',...
                    'YDir','reverse',...
                    'DrawMode','normal',...
                    'Visible','off',...
                    'Units','pixels',...
                    'Position',image_axes_position,...
                    'XLim',[0.5,n_cols+0.5],...
                    'YLim',[0.5,n_rows+0.5],...
                    'DataAspectRatio',[1 1 1],...
                    'ButtonDownFcn','show_callback');
image_h = image('Parent',image_axes_h,...
                'CData',this_frame,...
                'Tag','image_h',...
                'SelectionHighlight','off',...
                'EraseMode','none',...
                'ButtonDownFcn','show_callback');

% VCR-style controls
to_start_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','pushbutton',...
            'String','|<',...
            'Tag','to_start_button_h',...
            'Position',...
              [figure_left_pad_size+mode_button_width+...
                 button_image_pad_width+vcr_button_spacer_width/2+...
                 (1-1)*(vcr_button_width+vcr_button_spacer_width),...
               (figure_bottom_pad_size-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','show_callback');
play_backward_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','pushbutton',...
            'String','<',...
            'Tag','play_backward_button_h',...
            'Position',...
              [figure_left_pad_size+mode_button_width+...
                 button_image_pad_width+vcr_button_spacer_width/2+...
                 (2-1)*(vcr_button_width+vcr_button_spacer_width),...
               (figure_bottom_pad_size-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','show_callback');
frame_backward_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','pushbutton',...
            'String','<|',...
            'Tag','frame_backward_button_h',...
            'Position',...
              [figure_left_pad_size+mode_button_width+...
                 button_image_pad_width+vcr_button_spacer_width/2+...
                 (3-1)*(vcr_button_width+vcr_button_spacer_width),...
               (figure_bottom_pad_size-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','show_callback');
stop_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','pushbutton',...
            'String','O',...
            'Tag','stop_button_h',...
            'Position',...
              [figure_left_pad_size+mode_button_width+...
                 button_image_pad_width+vcr_button_spacer_width/2+...
                 (4-1)*(vcr_button_width+vcr_button_spacer_width),...
               (figure_bottom_pad_size-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','show_callback');
frame_forward_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','pushbutton',...
            'String','|>',...
            'Tag','frame_forward_button_h',...
            'Position',...
              [figure_left_pad_size+mode_button_width+...
                 button_image_pad_width+vcr_button_spacer_width/2+...
                 (5-1)*(vcr_button_width+vcr_button_spacer_width),...
               (figure_bottom_pad_size-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','show_callback');
play_forward_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','pushbutton',...
            'String','>',...
            'Tag','play_forward_button_h',...
            'Position',...
              [figure_left_pad_size+mode_button_width+...
                 button_image_pad_width+vcr_button_spacer_width/2+...
                 (6-1)*(vcr_button_width+vcr_button_spacer_width),...
               (figure_bottom_pad_size-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','show_callback');
to_end_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','pushbutton',...
            'String','>|',...
            'Tag','to_end_button_h',...
            'Position',...
              [figure_left_pad_size+mode_button_width+...
                 button_image_pad_width+vcr_button_spacer_width/2+...
                 (7-1)*(vcr_button_width+vcr_button_spacer_width),...
               (figure_bottom_pad_size-vcr_button_height)/2,...
               vcr_button_width,...
               vcr_button_height],...
             'Callback','show_callback');

% Frame index counter
frame_text_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','text',...
            'String','Frame ',...
            'Tag','frame_text_h',...
            'Callback','show_callback',...
            'BackgroundColor',get(show_figure_h,'Color'));
extent=get(frame_text_h,'Extent');
frame_text_width=extent(3);
frame_text_height=extent(4);
frame_index_edit_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','edit',...
            'String',sprintf('%d',n_frames),...
            'Tag','frame_index_edit_h',...
            'Min',0,...
            'Max',1,...
            'HorizontalAlignment','right',...
            'BackgroundColor','white',...
            'Callback','show_callback');
extent=get(frame_index_edit_h,'Extent');
frame_index_edit_width=extent(3)+10;  % need padding
frame_index_edit_height=extent(4)+2;  % need padding
set(frame_index_edit_h,...
    'String',sprintf('%d',frame_index));
of_n_frames_text_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','text',...
            'String',sprintf(' of %d',n_frames),...
            'Tag','of_n_frames_text_h',...
            'Callback','show_callback',...
            'BackgroundColor',get(show_figure_h,'Color'));
extent=get(of_n_frames_text_h,'Extent');
of_n_frames_text_width=extent(3);
of_n_frames_text_height=extent(4);
% set positions of frame counter elements
full_frame_text_width=frame_text_width+...
                      frame_index_edit_width+...
                      of_n_frames_text_width;
frame_text_left_margin=figure_left_pad_size+mode_button_width+...
                       button_image_pad_width;
frame_text_baseline=figure_height-(figure_top_pad_size+frame_text_height)/2;
set(frame_text_h,'Position',...
                 [frame_text_left_margin,...
                  frame_text_baseline,...
                  frame_text_width,...
                  frame_text_height]);
set(frame_index_edit_h,...
    'Position',[frame_text_left_margin+frame_text_width,...
                frame_text_baseline+...
                  (frame_text_height-frame_index_edit_height)/2+2,...
                frame_index_edit_width,...
                frame_index_edit_height]);
set(of_n_frames_text_h,'Position',...
                       [frame_text_left_margin+frame_text_width+...
                          frame_index_edit_width,...
                        frame_text_baseline,...
                        of_n_frames_text_width,...
                        of_n_frames_text_height]);

% Frames per second controls
if n_frames>1
  fps=1000*(n_frames-1)/(t_o(n_frames)-t_o(1));  % t_o is in msec
else
  fps=1;
end
FPS_text_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','text',...
            'String','FPS: ',...
            'Tag','FPS_text_h',...
            'Callback','show_callback',...
            'BackgroundColor',get(show_figure_h,'Color'));
extent=get(FPS_text_h,'Extent');
FPS_text_width=extent(3);
FPS_text_height=extent(4);
FPS_edit_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','edit',...
            'String',sprintf('%6.2f',fps),...
            'Tag','FPS_edit_h',...
            'Min',0,...
            'Max',1,...
            'HorizontalAlignment','right',...
            'BackgroundColor','white',...
            'Callback','show_callback');
extent=get(FPS_edit_h,'Extent');
FPS_edit_width=extent(3)+10;  % padding
FPS_edit_height=extent(4)+2;  % padding
% set positions of FPS elements
FPS_elements_width=FPS_text_width+FPS_edit_width;
FPS_elements_left_margin=figure_left_pad_size+mode_button_width+...
                         button_image_pad_width+image_area_width-...
                         FPS_elements_width;
FPS_elements_baseline=figure_height-(figure_top_pad_size+FPS_text_height)/2;
set(FPS_text_h,'Position',...
               [FPS_elements_left_margin,...
                FPS_elements_baseline,...
                FPS_text_width,...
                FPS_text_height]);
set(FPS_edit_h,...
    'Position',[FPS_elements_left_margin+FPS_text_width,...
                FPS_elements_baseline+...
                  (FPS_text_height-FPS_edit_height)/2+2,...
                FPS_edit_width,...
                FPS_edit_height]);

% Mode buttons
elliptic_roi_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','togglebutton',...
            'String','Elliptic ROI',...
            'Tag','elliptic_roi_button_h',...
            'Position',...
              [figure_left_pad_size,...
               figure_bottom_pad_size+image_area_height-...
                 mode_button_height-...
                 (1-1)*(mode_button_height+mode_button_spacer_height),...
               mode_button_width,...
               mode_button_height],...
            'Callback','show_callback',...
            'Value',1);
rect_roi_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','togglebutton',...
            'String','Rect ROI',...
            'Tag','rect_roi_button_h',...
            'Position',...
              [figure_left_pad_size,...
               figure_bottom_pad_size+image_area_height-...
                 mode_button_height-...
                 (2-1)*(mode_button_height+mode_button_spacer_height),...
               mode_button_width,...
               mode_button_height],...
            'Callback','show_callback');
edit_roi_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','togglebutton',...
            'String','Edit ROI',...
            'Tag','edit_roi_button_h',...
            'Position',...
              [figure_left_pad_size,...
               figure_bottom_pad_size+image_area_height-...
                 mode_button_height-...
                 (3-1)*(mode_button_height+mode_button_spacer_height),...
               mode_button_width,...
               mode_button_height],...
            'Enable','off',...
            'Callback','show_callback');
select_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','togglebutton',...
            'String','Select',...
            'Tag','select_button_h',...
            'Position',...
              [figure_left_pad_size,...
               figure_bottom_pad_size+image_area_height-...
                 mode_button_height-...
                 (4-1)*(mode_button_height+mode_button_spacer_height),...
               mode_button_width,...
               mode_button_height],...
            'Enable','off',...
            'Callback','show_callback');
zoom_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','togglebutton',...
            'String','Zoom',...
            'Tag','zoom_button_h',...
            'Position',...
              [figure_left_pad_size,...
               figure_bottom_pad_size+image_area_height-...
                 mode_button_height-...
                 (5-1)*(mode_button_height+mode_button_spacer_height),...
               mode_button_width,...
               mode_button_height],...
            'Callback','show_callback');
move_all_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','togglebutton',...
            'String','Move All',...
            'Tag','move_all_button_h',...
            'Position',...
              [figure_left_pad_size,...
               figure_bottom_pad_size+image_area_height-...
                 mode_button_height-...
                 (6-1)*(mode_button_height+mode_button_spacer_height),...
               mode_button_width,...
               mode_button_height],...
            'Enable','off',...
            'Callback','show_callback');
            

% Action buttons
chart_button_h = ...
  uicontrol('Parent',show_figure_h,...
            'Style','pushbutton',...
            'String','Chart',...
            'Tag','chart_button_h',...
            'Position',...
              [figure_left_pad_size,...
               figure_bottom_pad_size+...
                 (1-1)*(action_button_height+action_button_spacer_height),...
               action_button_width,...
               action_button_height],...
            'Callback','show_callback');
%polka_button_h = ...
%  uicontrol('Parent',show_figure_h,...
%            'Style','pushbutton',...
%            'String','Coherence',...
%            'Tag','polka_button_h',...
%            'Position',...
%              [figure_left_pad_size,...
%               figure_bottom_pad_size+...
%                 (2-1)*(action_button_height+action_button_spacer_height),...
%               action_button_width,...
%               action_button_height],...
%            'Callback','show_callback');
        
        

%
% add some menus
%
                     
% the Color menu
color_menu_h=uimenu(show_figure_h,...
                   'Tag','color_menu_h',...
                   'Label','Color');
min_max_menu_h=uimenu(color_menu_h,...
                      'Tag','min_max_menu_h',...
                      'Label','Minimum/Maximum',...
                      'Callback','show_callback');
five_95_menu_h=uimenu(color_menu_h,...
                      'Tag','five_95_menu_h',...
                      'Label','5%/95%',...
                      'Callback','show_callback');
abs_max_menu_h=uimenu(color_menu_h,...
                      'Tag','abs_max_menu_h',...
                      'Label','-Absolute Maximum/+Absolute Maximum',...
                      'Callback','show_callback');
ninety_symmetric_menu_h=uimenu(color_menu_h,...
                               'Tag','ninety_symmetric_menu_h',...
                               'Label','90% Symmetric',...
                               'Callback','show_callback');                   
colorbar_edit_bounds_menu_h=uimenu(color_menu_h,...
                                   'Tag','colorbar_edit_bounds_menu_h',...
                                   'Label','Edit Colorbar Bounds...',...
                                   'Callback','show_callback');
gray_menu_h=uimenu(color_menu_h,...
                   'Tag','gray_menu_h',...
                   'Label','Gray',...
                   'Callback','show_callback',...
                   'Checked','on',...
                   'Separator','on');
bone_menu_h=uimenu(color_menu_h,...
                   'Tag','bone_menu_h',...
                   'Label','Bone',...
                   'Callback','show_callback');
hot_menu_h=uimenu(color_menu_h,...
                  'Tag','hot_menu_h',...
                  'Label','Hot',...
                  'Callback','show_callback');
jet_menu_h=uimenu(color_menu_h,...
                  'Tag','jet_menu_h',...
                  'Label','Jet',...
                  'Callback','show_callback');
red_green_menu_h=uimenu(color_menu_h,...
                        'Tag','red_green_menu_h',...
                        'Label','Red/Green',...
                        'Callback','show_callback');
red_blue_h=uimenu(color_menu_h,...
                  'Tag','red_blue_menu_h',...
                  'Label','Red/Blue',...
                  'Callback','show_callback');               
brighten_menu_h=uimenu(color_menu_h,...
                       'Label','Brighten',...
                       'Tag','brighten_menu_h',...
                       'Accelerator','b',...
                       'Callback','show_callback',...
                       'Separator','on');
darken_menu_h=uimenu(color_menu_h,...
                     'Label','Darken',...
                     'Tag','darken_menu_h',...
                     'Accelerator','d',...
                     'Callback','show_callback');
revert_menu_h=uimenu(color_menu_h,...
                     'Label','Revert',...
                     'Tag','revert_menu_h',...                     
                     'Accelerator','r',...
                     'Callback','show_callback');

% the ROI menu
rois_menu_h=uimenu(show_figure_h,...
                   'Tag','rois_menu_h',...
                   'Label','ROIs');
open_rois_menu_h=uimenu(rois_menu_h,...
                        'Label','Open...',...
                        'Tag','open_rois_menu_h',...
                        'Callback','show_callback');
save_rois_to_file_menu_h=uimenu(rois_menu_h,...
                                'Label','Save...',...
                                'Tag','save_rois_to_file_menu_h',...
                                'Callback','show_callback',...
                                'Enable','off');               
rename_roi_menu_h=uimenu(rois_menu_h,...
                         'Label','Rename Selected',...
                         'Tag','rename_roi_menu_h',...
                         'Callback','show_callback',...
                         'Enable','off',...
                         'Separator','on');
delete_roi_menu_h=uimenu(rois_menu_h,...
                         'Label','Delete Selected',...
                         'Tag','delete_roi_menu_h',...
                         'Callback','show_callback',...
                         'Enable','off');
delete_all_rois_menu_h=uimenu(rois_menu_h,...
                              'Label','Delete All',...
                              'Tag','delete_all_rois_menu_h',...
                              'Callback','show_callback',...
                              'Enable','off');                     
hide_rois_menu_h=uimenu(rois_menu_h,...
                        'Label','Hide ROIs',...
                        'Tag','hide_rois_menu_h',...
                        'Callback','show_callback',...
                        'Enable','off',...
                        'Separator','on');                     

% the Print menu
printing_menu_h=uimenu(show_figure_h,...
                       'Tag','printing_menu_h',...
                       'Label','Print');
page_setup_menu_h=uimenu(printing_menu_h,...
                         'Label','Page Setup...',...
                         'Tag','page_setup_menu_h',...
                         'Callback','show_callback');
%print_setup_menu_h=uimenu(printing_menu_h,...
%                         'Label','Print Setup...',...
%                         'Tag','print_setup_menu_h',...
%                         'Callback','show_callback');
print_preview_menu_h=uimenu(printing_menu_h,...
                         'Label','Print Preview...',...
                         'Tag','print_preview_menu_h',...
                         'Callback','show_callback');
print_menu_h=uimenu(printing_menu_h,...
                    'Label','Print...',...
                    'Tag','print_menu_h',...
                    'Accelerator','p',...
                    'Callback','show_callback');
                          


%          
% set up the figure state variables
%
set_userdata(show_figure_h,'serial_number',now);
set_userdata(show_figure_h,'frame_index',frame_index);
% this holds the _playback_ frame rate, in frames/sec
set_userdata(show_figure_h,'fps',fps);
set_userdata(show_figure_h,'stop_button_hit',0);
% this is the current selection mode
set_userdata(show_figure_h,'mode',mode);
set_userdata(show_figure_h,'cmap_name',cmap_name);
set_userdata(show_figure_h,'colorbar_max_string',colorbar_max_string);
set_userdata(show_figure_h,'colorbar_min_string',colorbar_min_string);
set(image_axes_h,'UserData',indexed_data);
set(image_h,'UserData',data);
set(colorbar_h,'UserData',struct('t_o',t_o,'t_e',t_e,'e_phys',e_phys));
% roi state
set_userdata(show_figure_h,'n_rois',0);
set_userdata(colorbar_axes_h,'roi_ids',[]);
set_userdata(colorbar_axes_h,'border_h',[]);
set_userdata(colorbar_axes_h,'label_h',[]);
set_userdata(show_figure_h,'selected_roi_index',NaN);
set_userdata(show_figure_h,'hide_rois',0);

% have to do this last, otherwise having the pointer in the place
% where the window appears causes an error at startup
set(show_figure_h,'WindowButtonMotionFcn','show_update_pointer');
