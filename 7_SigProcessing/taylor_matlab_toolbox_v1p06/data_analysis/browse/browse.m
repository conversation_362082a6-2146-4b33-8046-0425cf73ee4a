function browse_figure_h=f(t,data,...
                           ts,...
                           ts_on,ts_off,...
                           names)

% This is browse, a function for browsing multi-channel electrophysiology
% data.
%
% t should be a col vector, and should be in seconds
% data should be length(t) x n, where n is the number of signals

% check for row-vectorness, transpose if
if size(t,1)==1 && size(t,2)>1
  t=t';
  data=data';
end

% get the number of channels, etc.
n_t=length(t);
n=size(data,2);

% deal w/ args
if nargin<3 || length(ts)==0
  ts=cell(n,1);
end
if nargin<4 || length(ts_on)==0
  ts_on=cell(n,1);
end
if nargin<5 || length(ts_off)==0
  ts_off=cell(n,1);
end
if nargin<6 || length(names)==0
  names=cell(n,1);
end

% get the screen size so we can position the figure window
root_units=get(0,'Units');
set(0,'Units','pixels');
screen_dims=get(0,'ScreenSize');
screen_width=screen_dims(3);
screen_height=screen_dims(4); 
set(0,'Units',root_units);



%
% spec out the layout of the figure
%

% layout of the figure on the screen
screen_left_pad_size=100;
screen_top_pad_size=100;

% the figure
plot_area_width=screen_width-2*screen_left_pad_size;
total_plot_area_height=screen_height-3*screen_top_pad_size;
interplot_pad_height=0;
plot_area_height=floor((total_plot_area_height- ...
                        (n-1)*interplot_pad_height)/n);
total_plot_area_height=n*plot_area_height+(n-1)*interplot_pad_height;
x_label_area_height=50;
y_label_area_width=50;
bottom_button_height=30;
bottom_button_width=60;
n_scroll_buttons=6;
n_zoom_buttons=3;
n_bottom_buttons=n_scroll_buttons+n_zoom_buttons;
button_intergroup_space_width=80;
bottom_button_spacer_width=20;
figure_right_pad_size=20;
figure_left_pad_size=20;
figure_top_pad_size=20;
figure_bottom_pad_size=20;
figure_width=figure_left_pad_size+y_label_area_width+plot_area_width+...
             figure_right_pad_size;
figure_height=figure_top_pad_size+...
              total_plot_area_height+...
              x_label_area_height+...
              bottom_button_height+...
              figure_bottom_pad_size;



%
% make the figure
%
browse_figure_h = ...
  figure('Tag','browse_figure_h',...
         'Position',[(screen_width-figure_width)/2,...
                     (screen_height-figure_height)/2,...
                     figure_width,figure_height],...
         'Name','Browse',...
         'NumberTitle','off',...
         'Resize','off',...
         'MenuBar','none',...
         'PaperPositionMode','auto',...
         'Renderer','zbuffer');
%plotedit(browse_figure_h,'hidetoolsmenu');



%
% add some menus
%

% the x-axis menu
x_axis_menu_h=uimenu(browse_figure_h,...
                    'Tag','x_axis_menu_h',...
                    'Label','X Axis');
time_ms_menu_h=uimenu(x_axis_menu_h,...
                      'Tag','time_ms_menu_h',...
                      'Label','Time (ms)',...
                      'Callback','browse_callback');
time_s_menu_h=uimenu(x_axis_menu_h,...
                     'Tag','time_s_menu_h',...
                     'Label','Time (s)',...
                     'Callback','browse_callback',...
                     'Checked','on');
sample_number_menu_h=uimenu(x_axis_menu_h,...
                            'Tag','sample_number_menu_h',...
                            'Label','Sample Number',...
                            'Callback','browse_callback');

% the Print menu
printing_menu_h=uimenu(browse_figure_h,...
                       'Tag','printing_menu_h',...
                       'Label','Print');
page_setup_menu_h=uimenu(printing_menu_h,...
                         'Label','Page Setup...',...
                         'Tag','page_setup_menu_h',...
                         'Callback','browse_callback');
print_preview_menu_h=uimenu(printing_menu_h,...
                         'Label','Print Preview...',...
                         'Tag','print_preview_menu_h',...
                         'Callback','browse_callback');
print_menu_h=uimenu(printing_menu_h,...
                    'Label','Print...',...
                    'Tag','print_menu_h',...
                    'Accelerator','p',...
                    'Callback','browse_callback');

%
% add some buttons
%
% scroll buttons
to_start_button_h = ...
  uicontrol('Parent',browse_figure_h,...
            'Style','pushbutton',...
            'String','|<',...
            'Tag','to_start_button_h',...
            'Position',...
              [figure_left_pad_size+...
                 y_label_area_width+...
                 (1-1)*(bottom_button_width+bottom_button_spacer_width),...
               figure_bottom_pad_size,...
               bottom_button_width,...
               bottom_button_height],...
             'Callback','browse_callback');
page_left_button_h = ...
  uicontrol('Parent',browse_figure_h,...
            'Style','pushbutton',...
            'String','<<',...
            'Tag','page_left_button_h',...
            'Position',...
              [figure_left_pad_size+...
                 y_label_area_width+...
                 (2-1)*(bottom_button_width+bottom_button_spacer_width),...
               figure_bottom_pad_size,...
               bottom_button_width,...
               bottom_button_height],...
             'Callback','browse_callback');
step_left_button_h = ...
  uicontrol('Parent',browse_figure_h,...
            'Style','pushbutton',...
            'String','<',...
            'Tag','step_left_button_h',...
            'Position',...
              [figure_left_pad_size+...
                 y_label_area_width+...
                 (3-1)*(bottom_button_width+bottom_button_spacer_width),...
               figure_bottom_pad_size,...
               bottom_button_width,...
               bottom_button_height],...
             'Callback','browse_callback');
step_right_button_h = ...
  uicontrol('Parent',browse_figure_h,...
            'Style','pushbutton',...
            'String','>',...
            'Tag','step_right_button_h',...
            'Position',...
              [figure_left_pad_size+...
                 y_label_area_width+...
                 (4-1)*(bottom_button_width+bottom_button_spacer_width),...
               figure_bottom_pad_size,...
               bottom_button_width,...
               bottom_button_height],...
             'Callback','browse_callback');
page_right_button_h = ...
  uicontrol('Parent',browse_figure_h,...
            'Style','pushbutton',...
            'String','>>',...
            'Tag','page_right_button_h',...
            'Position',...
              [figure_left_pad_size+...
                 y_label_area_width+...
                 (5-1)*(bottom_button_width+bottom_button_spacer_width),...
               figure_bottom_pad_size,...
               bottom_button_width,...
               bottom_button_height],...
             'Callback','browse_callback');
to_end_button_h = ...
  uicontrol('Parent',browse_figure_h,...
            'Style','pushbutton',...
            'String','>|',...
            'Tag','to_end_button_h',...
            'Position',...
              [figure_left_pad_size+...
                 y_label_area_width+...
                 (6-1)*(bottom_button_width+bottom_button_spacer_width),...
               figure_bottom_pad_size,...
               bottom_button_width,...
               bottom_button_height],...
             'Callback','browse_callback');

% zoom buttons
zoom_way_out_button_h = ...
  uicontrol('Parent',browse_figure_h,...
            'Style','pushbutton',...
            'String','--',...
            'Tag','zoom_way_out_button_h',...
            'Position',...
              [figure_width-figure_right_pad_size-...
                 (3)*bottom_button_width-...
                 (3-1)*bottom_button_spacer_width,...
               figure_bottom_pad_size,...
               bottom_button_width,...
               bottom_button_height],...
             'Callback','browse_callback');
zoom_out_button_h = ...
  uicontrol('Parent',browse_figure_h,...
            'Style','pushbutton',...
            'String','-',...
            'Tag','zoom_out_button_h',...
            'Position',...
              [figure_width-figure_right_pad_size-...
                 (2)*bottom_button_width-...
                 (2-1)*bottom_button_spacer_width,...
               figure_bottom_pad_size,...
               bottom_button_width,...
               bottom_button_height],...
             'Callback','browse_callback');
zoom_in_button_h = ...
  uicontrol('Parent',browse_figure_h,...
            'Style','pushbutton',...
            'String','+',...
            'Tag','zoom_in_button_h',...
            'Position',...
              [figure_width-figure_right_pad_size-...
                 (1)*bottom_button_width-...
                 (1-1)*bottom_button_spacer_width,...
               figure_bottom_pad_size,...
               bottom_button_width,...
               bottom_button_height],...
             'Callback','browse_callback');

                  
%
% make a color sequence for traces
%
colors=make_color_sequence;

                                      

%
% initialize the plot axes
%

% create the signal plot axes
axes_hs=zeros(n,1);
for i=1:n
  plot_axes_position=...
      [figure_left_pad_size+y_label_area_width,...
       figure_bottom_pad_size+...
         x_label_area_height+...
         bottom_button_height+...
         (n-i)*plot_area_height+...
         (n-i)*interplot_pad_height,...
       plot_area_width,...
       plot_area_height];
  tag=sprintf('axes_hs(%d)',i);
  axes_hs(i)=axes('Parent',browse_figure_h,...
                  'Tag',tag,...
                  'Units','pixels',...
                  'Position',plot_axes_position,...
                  'Box','on',...
                  'Layer','Top',...
                  'ButtonDownFcn','browse_draw_zoom_limits(''start'')');
  if i<n
    set(gca,'XTickLabel',{});
  else
    xlabel('t (s)');
  end
end

% put the signals in the axes
for i=1:n
  set(axes_hs(i),'XLim',[t(1) t(end)]);
  y_min=min(data(:,i));  y_max=max(data(:,i));
  y_mid=(y_min+y_max)/2;  y_radius=(y_max-y_min)/2;
  y_lo=y_mid-1.1*y_radius;  y_hi=y_mid+1.1*y_radius;
  set(axes_hs(i),'YLim',[y_lo y_hi]);
  axes(axes_hs(i));
  ylabel(names{i});
  line('Parent',axes_hs(i),...
       'XData',t,...
       'YData',data(:,i),...
       'Color',colors(i,:));
end

% put the events in the axes
for i=1:n
  ts_this=ts{i};
  yl=ylim(axes_hs(i));
  y_range=yl(2)-yl(1);
  y_span=[yl(1)+0.05*y_range yl(1)+0.15*y_range];
  %y_span=0.9*yl;
  axes(axes_hs(i));
  for j=1:length(ts_this)
    line([ts_this(j) ts_this(j)],...
         y_span,...
         [-2 -2],...
         'Color',0.75*[1 1 1]);
  end
end
       
% put the burst starts in the axes
for i=1:n
  starts=ts_on{i};
  yl=ylim(axes_hs(i));
  y_range=yl(2)-yl(1);
  y_span=[yl(1)+0.05*y_range yl(1)+0.15*y_range];
  %y_span=0.9*yl;
  axes(axes_hs(i));
  for j=1:length(starts)
    line([starts(j) starts(j)],...
         y_span,...
         [-1 -1],...
         'Color',[0 0.75 0]);
  end
end

% put the burst ends in the axes
for i=1:n
  ends=ts_off{i};
  yl=ylim(axes_hs(i));
  y_range=yl(2)-yl(1);
  y_span=[yl(1)+0.05*y_range yl(1)+0.15*y_range];
  %y_span=0.9*yl;
  axes(axes_hs(i));
  for j=1:length(ends)
    line([ends(j) ends(j)],...
         y_span,...
         [-1 -1],...
         'Color',[1 0 0]);       
  end
end



%          
% set up the state variables
%
set_userdata(browse_figure_h,'serial_number',0);
set_userdata(browse_figure_h,'colors',colors);
set_userdata(browse_figure_h,'plot_x_axis','time_s');
set_userdata(browse_figure_h,'axes_hs',axes_hs);
set_userdata(x_axis_menu_h,'t',t);
set_userdata(x_axis_menu_h,'data',data);

