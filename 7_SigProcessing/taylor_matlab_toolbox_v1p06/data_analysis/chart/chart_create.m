function chart_figure_h=f(t_o,roi_sums,roi_n_pels,...
                          roi_ids,roi_labels,...
                          t_e,e_phys,serial_number)

% t_o must be a col vector
% rows of roi_sums must index samples, cols must index rois

% get the screen size so we can position the figure window
root_units=get(0,'Units');
set(0,'Units','pixels');
screen_dims=get(0,'ScreenSize');
screen_width=screen_dims(3);
screen_height=screen_dims(4);
set(0,'Units',root_units);

%
% spec out the layout of the figure
%

% layout of the figs numbers
screen_left_pad_size=480;
screen_top_pad_size=50;

% the figure
n_plot_areas=5;
plot_area_width=400;
plot_area_height=80;
interplot_pad_height=30;
roi_listbox_width=50;
roi_listbox_plot_spacer_width=10;
figure_right_pad_size=10;
figure_left_pad_size=60;
figure_top_pad_size=30;
figure_bottom_pad_size=50;
figure_width=figure_left_pad_size+plot_area_width+...
             roi_listbox_plot_spacer_width+roi_listbox_width+...
             figure_right_pad_size;
figure_height=figure_top_pad_size+...
              n_plot_areas*plot_area_height+...
              (n_plot_areas-1)*interplot_pad_height+...
              figure_bottom_pad_size;



%
% make the figure
%
chart_figure_h = ...
  figure('Tag','chart_figure_h',...
         'Position',[screen_left_pad_size,...
                     screen_height-figure_height-...
                     screen_top_pad_size+1,...
                     figure_width,figure_height],...
         'Name','Chart',...
         'NumberTitle','off',...
         'Resize','off',...
         'MenuBar','none',...
         'PaperPositionMode','auto');
%plotedit(chart_figure_h,'hidetoolsmenu');
     
% create the optical plot axes
plot_axes_position=...
  [figure_left_pad_size,...
   figure_bottom_pad_size+...
     (n_plot_areas-1)*plot_area_height+...
     (n_plot_areas-1)*interplot_pad_height,...
   plot_area_width,...
   plot_area_height];
optical_axes_h = axes('Parent',chart_figure_h,...
                   'Tag','optical_axes_h',...
                   'Units','pixels',...
                   'Position',plot_axes_position,...
                   'Box','on',...
                   'Layer','Top');

% create the voltage plot axes
voltage_axes_position=...
  [figure_left_pad_size,...
   figure_bottom_pad_size+...
     (n_plot_areas-2)*plot_area_height+...
     (n_plot_areas-2)*interplot_pad_height,...
   plot_area_width,...
   plot_area_height];
voltage_axes_h = axes('Parent',chart_figure_h,...
                      'Tag','voltage_axes_h',...
                      'Units','pixels',...
                      'Position',voltage_axes_position,...
                      'Box','on',...
                      'Layer','Top');

% create the current plot axes
current_axes_position=...
  [figure_left_pad_size,...
   figure_bottom_pad_size+...
     (n_plot_areas-3)*plot_area_height+...
     (n_plot_areas-3)*interplot_pad_height,...
   plot_area_width,...
   plot_area_height];
current_axes_h = axes('Parent',chart_figure_h,...
                      'Tag','current_axes_h',...
                      'Units','pixels',...
                      'Position',current_axes_position,...
                      'Box','on',...
                      'Layer','Top');

% create the extra plot axes
extra_axes_position=...
  [figure_left_pad_size,...
   figure_bottom_pad_size+...
     (n_plot_areas-4)*plot_area_height+...
     (n_plot_areas-4)*interplot_pad_height,...
   plot_area_width,...
   plot_area_height];
extra_axes_h = axes('Parent',chart_figure_h,...
                    'Tag','extra_axes_h',...
                    'Units','pixels',...
                    'Position',extra_axes_position,...
                    'Box','on',...
                    'Layer','Top');

% create the ttl plot axes
ttl_axes_position=...
  [figure_left_pad_size,...
   figure_bottom_pad_size+...
     (n_plot_areas-5)*plot_area_height+...
     (n_plot_areas-5)*interplot_pad_height,...
   plot_area_width,...
   plot_area_height];
ttl_axes_h = axes('Parent',chart_figure_h,...
                  'Tag','ttl_axes_h',...
                  'Units','pixels',...
                  'Position',ttl_axes_position,...
                  'Box','on',...
                  'Layer','Top');

% create the ROI listbox
roi_listbox_position=[figure_left_pad_size+plot_area_width+...
                        roi_listbox_plot_spacer_width,...
                      figure_bottom_pad_size,...
                      roi_listbox_width,...
                      n_plot_areas*plot_area_height+...
                        (n_plot_areas-1)*interplot_pad_height];
roi_listbox_h=uicontrol('Parent',chart_figure_h,...
                        'Tag','roi_listbox_h',...
                        'Style','listbox',...
                        'Position',roi_listbox_position,...
                        'BackgroundColor',[1 1 1],...
                        'Callback','chart_callback',...
                        'Min',0,...
                        'Max',2);
                        


%
% add some menus
%

% the x-axis menu
xaxis_menu_h=uimenu(chart_figure_h,...
                    'Tag','xaxis_menu_h',...
                    'Label','X Axis');
time_sec_menu_h=uimenu(xaxis_menu_h,...
                       'Tag','time_sec_menu_h',...
                       'Label','Time (s)',...
                       'Callback','chart_callback',...
                       'Checked','on');
%time_sec_menu_h=uimenu(xaxis_menu_h,...
%                       'Tag','time_sec_menu_h',...
%                       'Label','Time (sec)',...
%                       'Callback','chart_callback');
frame_number_menu_h=uimenu(xaxis_menu_h,...
                           'Tag','frame_number_menu_h',...
                           'Label','Frame Number',...
                           'Callback','chart_callback');

% the optical axis menu                
optical_axis_menu_h=uimenu(chart_figure_h,...
                    'Tag','optical_axis_menu_h',...
                    'Label','Optical');
autoscale_menu_h=uimenu(optical_axis_menu_h,...
                        'Tag','autoscale_menu_h',...
                        'Label','Autoscale',...
                        'Callback','chart_callback',...
                        'Checked','on');
edit_bounds_menu_h=uimenu(optical_axis_menu_h,...
                          'Tag','edit_bounds_menu_h',...
                          'Label','Edit Bounds...',...
                          'Callback','chart_callback');
mean_menu_h=uimenu(optical_axis_menu_h,...
                   'Tag','mean_menu_h',...
                   'Label','Mean',...
                   'Callback','chart_callback',...
                   'Separator','on');
mean_ac_menu_h=uimenu(optical_axis_menu_h,...
                      'Tag','mean_ac_menu_h',...
                      'Label','Mean AC',...
                      'Callback','chart_callback');
sum_menu_h=uimenu(optical_axis_menu_h,...
                  'Tag','sum_menu_h',...
                  'Label','Sum',...
                  'Callback','chart_callback');
sum_ac_menu_h=uimenu(optical_axis_menu_h,...
                     'Tag','sum_ac_menu_h',...
                     'Label','Sum AC',...
                     'Callback','chart_callback');
dff_menu_h=uimenu(optical_axis_menu_h,...
                  'Tag','dff_menu_h',...
                  'Label','Percent Change (dF/F)',...
                  'Checked','on',...
                  'Callback','chart_callback');
no_debleach_menu_h=uimenu(optical_axis_menu_h,...
                          'Tag','no_debleach_menu_h',...
                          'Label','No Debleaching',...
                          'Callback','chart_callback',...
                          'Separator','on');
linear_debleach_menu_h=uimenu(optical_axis_menu_h,...
                         'Tag','linear_debleach_menu_h',...
                         'Label','Linear',...
                         'Callback','chart_callback');
quadratic_debleach_menu_h=uimenu(optical_axis_menu_h,...
                            'Tag','quadratic_debleach_menu_h',...
                            'Label','Quadratic',...
                            'Callback','chart_callback');
cubic_debleach_menu_h=uimenu(optical_axis_menu_h,...
                        'Tag','cubic_debleach_menu_h',...
                        'Label','Cubic',...
                        'Callback','chart_callback');
quartic_debleach_menu_h=uimenu(optical_axis_menu_h,...
                          'Tag','quartic_debleach_menu_h',...
                          'Label','Quartic',...
                          'Callback','chart_callback');
quintic_debleach_menu_h=uimenu(optical_axis_menu_h,...
                         'Tag','quintic_debleach_menu_h',...
                         'Label','Quintic',...
                         'Callback','chart_callback');                       
lpf_debleach_menu_h=uimenu(optical_axis_menu_h,...
                           'Tag','lpf_debleach_menu_h',...
                           'Label','Low-Pass Filter',...
                           'Callback','chart_callback',...
                           'Checked','on');
                           
% the traces menu                       
traces_menu_h=uimenu(chart_figure_h,...
                   'Tag','traces_menu_h',...
                   'Label','Traces');
select_all_menu_h=uimenu(traces_menu_h,...
                        'Tag','select_all_menu_h',...
                        'Label','Select All',...
                        'Callback','chart_callback');
select_none_menu_h=uimenu(traces_menu_h,...
                         'Tag','select_none_menu_h',...
                         'Label','Select None',...
                         'Callback','chart_callback');
next_trace_menu_h=uimenu(traces_menu_h,...
                         'Tag','next_trace_menu_h',...
                         'Label','Next',...
                         'Callback','chart_callback',...
                         'Accelerator','f',...
                         'Enable','off',...
                         'Separator','on');
previous_trace_menu_h=uimenu(traces_menu_h,...
                         'Tag','previous_trace_menu_h',...
                         'Label','Previous',...
                         'Callback','chart_callback',...
                         'Enable','off',...
                         'Accelerator','b');
% the Print menu
printing_menu_h=uimenu(chart_figure_h,...
                       'Tag','printing_menu_h',...
                       'Label','Print');
page_setup_menu_h=uimenu(printing_menu_h,...
                         'Label','Page Setup...',...
                         'Tag','page_setup_menu_h',...
                         'Callback','chart_callback');
%print_setup_menu_h=uimenu(printing_menu_h,...
%                         'Label','Print Setup...',...
%                         'Tag','print_setup_menu_h',...
%                         'Callback','chart_callback');
print_preview_menu_h=uimenu(printing_menu_h,...
                         'Label','Print Preview...',...
                         'Tag','print_preview_menu_h',...
                         'Callback','chart_callback');
print_menu_h=uimenu(printing_menu_h,...
                    'Label','Print...',...
                    'Tag','print_menu_h',...
                    'Accelerator','p',...
                    'Callback','chart_callback');
                     


%          
% set up the state variables
%
set_userdata(chart_figure_h,'serial_number',serial_number);
set_userdata(chart_figure_h,'n_rois',0);
set_userdata(chart_figure_h,'roi_ids',[]);
set_userdata(chart_figure_h,'roi_labels',cell(0,0));
set_userdata(chart_figure_h,'trace_menu_h',[]);
set_userdata(chart_figure_h,'trace_h',[]);
set_userdata(chart_figure_h,'trace_on',[]);
set_userdata(chart_figure_h,'autoscale',logical(1));
set_userdata(chart_figure_h,'y_max_string','1');
set_userdata(chart_figure_h,'y_min_string','0');
set_userdata(chart_figure_h,'plot_y_axis','dff');
% this specifies what order of debleaching to apply
% NaN means no debleaching, 1 means linear debleaching, 2 means quadratic
% debleaching, etc.
set_userdata(chart_figure_h,'debleach_type','lpf');
set_userdata(chart_figure_h,'plot_x_axis','time_msec');
set_userdata(optical_axes_h,'t_o',[]);
set_userdata(optical_axes_h,'roi_sums',[]);
set_userdata(optical_axes_h,'roi_n_pels',[]);
set_userdata(xaxis_menu_h,'t_e',[]);
set_userdata(xaxis_menu_h,'e_phys',[]);
set_userdata(chart_figure_h,'e_phys_trace_h',[]);
