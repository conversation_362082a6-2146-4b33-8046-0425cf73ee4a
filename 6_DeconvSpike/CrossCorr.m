%ID = [];
numROIs = length(ce);
numCorr = nchoosek(numROIs,2);
ROICorr = zeros (4999, numCorr);

Thresd = 0.03;
count =0;
for i = 1:numROIs
    %plot(ce(i).df_f_resamp_raw);
    ii = i+1;
    for j = ii:numROIs
        count = count+1;
        corrTraces(count).firstCell = i;
        corrTraces(count).secondCell = j;
        
      a = ce(26).dffRes;
%       [b,a]=butter(2,[.15,.3]);
      %a1= butterworthbpf(a, 1, 50, 4);
      [ca, sa, ba] = deconvolveCa(a, 'ar1', 0.95, 1 , 1);
      b = ce(14).dffRes;
%       b(b<-.1) = -.1;
      [cb, sb, bb] = deconvolveCa(b, 'ar1',0.95, 1 , 1);
      figure
      [acor,lag] = xcorr(sa,sb);
%       ROICorr(:,count) = acor;
%       corrTraces(count).firstdeCon = sa;
%       corrTraces(count).seconddeCon = sb;
%       corrTraces(count).corrtrace = acor;
      subplot(5,1,1); plot(a); title(['ROI ', num2str(26), ' dF/F']);
      xticklabels({'0', '250', '500', '750', '1000' '1250 sec'}) 
      
      subplot(5,1,2); plot(b); title(['ROI ', num2str(14), ' dF/F']);
      xticklabels({'0', '250', '500', '750', '1000' '1250 sec'})
      subplot(5,1,3); plot(sa); title(['ROI ', num2str(26), ' deconvolved signal']);
      xticklabels({'0', '250', '500', '750', '1000' '1250 sec'}) 
      subplot(5,1,4); plot(sb); title(['ROI ', num2str(14), ' deconvolved signal']);
      xticklabels({'0', '250', '500', '750', '1000' '1250 sec'}) 
      subplot(5,1,5); plot(lag,acor); title('Cross-correlation')
      xticklabels({'-1250', '-1000', '-750', '-500', '-250', '0', '250', '500', '750', '1000' '1250'}) 
%       subplot(3,1,(i-1)*3+3+c); plot(lag,acor); title('Cross-correlation')
    end
end

[Height, Width] = size(ROICorr);
ROICorr_thresd = ROICorr;

for k = 1:Width
    
    if ROICorr_thresd([2499:2501],k) <= Thresd
       ROICorr_thresd(:,k) = 0;
    end
    corrTraces(count).highcorrtrace = ROICorr_thresd;
end

ROICorr_thresd( :, all(~ROICorr_thresd,1) ) = [];
plot(ROICorr_thresd)
xticklabels({'-2500', '-2000', '-1500', '-1000', '-500', '0', '500', '1000', '1500', '2000' '2500'}) 
axis([0 5000 0 1])
    
    
    