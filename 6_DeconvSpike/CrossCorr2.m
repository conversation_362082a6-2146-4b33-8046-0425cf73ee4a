%ID = [];
numROIs = 36;%length(ce);
% numCorr = nchoosek(numROIs,2);
ROICorr = zeros (numROIs, numROIs);

count =0;
for i = 1:numROIs
    for j = 1:numROIs
        count = count+1;
%         corrTraces(count).firstCell = i;
%         corrTraces(count).secondCell = j;
        
      a = ce(i).dffRes;
%       [b,a]=butter(2,[.15,.3]);
      %a1= butterworthbpf(a, 1, 50, 4);
      [ca, sa, ba] = deconvolveCa(a, 'ar1', 0.95, 1 , 1);
      b = ce(j).dffRes;
%       b(b<-.1) = -.1;
      [cb, sb, bb] = deconvolveCa(b, 'ar1',0.95, 1 , 1);
%       figure
      r = corr2(sa,sb);
      ROICorr(i,j) = r;
%       corrTraces(count).firstdeCon = sa;
%       corrTraces(count).seconddeCon = sb;
%       corrTraces(count).corrtrace = r;
%       subplot(3,1,1); plot(sa); title(['ROI ', num2str(i)]);
%       subplot(3,1,2); plot(sb); title(['ROI ', num2str(j)]);
%       subplot(3,1,3); plot(lag,acor); title('Cross-correlation')
%       subplot(3,1,(i-1)*3+3+c); plot(lag,acor); title('Cross-correlation')
    end
end

% [Height, Width] = size(ROICorr);
% ROICorr_thresd = ROICorr;

% plot(ROICorr)
% xticklabels({'-2500', '-2000', '-1500', '-1000', '-500', '0', '500', '1000', '1500', '2000' '2500'}) 
% axis([0 inf 0 1])
  
  images = image(ROICorr,'CDataMapping','scaled');
  caxis([0 .7])
  axis square
  colormap jet  
  set(gca,'XAxisLocation','top','YAxisLocation','left','ydir','reverse');
  colorbar
  tightfig;
  saveas(images, 'corrmap_2.bmp');
  
  
    
    