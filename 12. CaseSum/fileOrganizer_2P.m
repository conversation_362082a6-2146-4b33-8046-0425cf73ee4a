

% File organizer
clc
clear;
dataFolder = '/media/vcl/VCL004/AG4/2P_data/U001/';
% resultFolderRoot = 'O:\AF4\2P_analysis\';
cd(dataFolder)
dataFiles = dir(fullfile(dataFolder, '**', '*.signals'));
dataFiles = dir(fullfile(dataFolder, '**', '*.align'));
dataFiles = dir(fullfile(dataFolder, '**','*.segment'));

for i_0 = 1:3
dataFiles
numFiles = size(dataFiles, 1);

for i = 1:numFiles
%     i=1
    dname = dataFiles(i).name
    fname = dataFiles(i).folder;
    cd(fname)
    
    unit = dname(5:7);
%     plane = '000';
%     unit = dname(6:8);
%     unit = dname(14:16);
    plane = dname(16:18);
    folderName1 = [dname(5:11), '_', plane];
    resultFolder1 = strcat(dataFolder(1:6), '/2P_analysis/', 'U', unit, '/', folderName1, '/DataExport/' );
    
%     mapName = strcat(dataFiles(i).name(1:end-6), '_aligned map');
    folderName2 = [dname(5:11)];  
    resultFolder2 = strcat(fname, '/', folderName2);
    resultFolder3 = strcat(fname, '/metaFiles');
%     
%     if ~isfolder(resultFolder1)
%         mkdir(resultFolder1);    
%     end
%     if ~isfolder(resultFolder2)
%         mkdir(resultFolder2);    
%     end
%     if ~isfolder(resultFolder3)
%         mkdir(resultFolder3);    
%     end

    % Copy data files into the analysis folder
%     copyfile([dname(1:end-8), '.segment'], resultFolder1);
%     copyfile([dname(1:end-8), '.align'], resultFolder1);

    % move data files into the folder
%     movefile([dname(1:end-8), '_ot_parse.scan'], resultFolder3);
%     movefile([dname(1:11), '_ot_parse.scan'], resultFolder3);
    movefile([dname(1:end-6), '.*'], resultFolder2);

end
end