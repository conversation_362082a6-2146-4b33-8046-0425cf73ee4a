

% File organizer
clc
clear;
dataFolder = '/media/vcl/VCL004/AG4/2P_data/U001/';
% resultFolderRoot = 'O:\AF4\2P_analysis\';
cd(dataFolder)
% Get all relevant data files
signalFiles = dir(fullfile(dataFolder, '**', '*.signals'));
alignFiles = dir(fullfile(dataFolder, '**', '*.align'));
segmentFiles = dir(fullfile(dataFolder, '**','*.segment'));
% Combine all files
dataFiles = [signalFiles; alignFiles; segmentFiles];

for i_0 = 1:3
dataFiles
numFiles = size(dataFiles, 1);

for i = 1:numFiles
%     i=1
    dname = dataFiles(i).name;
    fname = dataFiles(i).folder;
    cd(fname)

    % Get file extension to determine file type
    [~, baseName, ext] = fileparts(dname);

    % Extract unit and plane information
    unit = dname(5:7);
%     plane = '000';
%     unit = dname(6:8);
%     unit = dname(14:16);
    plane = dname(16:18);
    folderName1 = [dname(5:11), '_', plane];
    resultFolder1 = strcat(dataFolder(1:6), '/2P_analysis/', 'U', unit, '/', folderName1, '/DataExport/' );

%     mapName = strcat(dataFiles(i).name(1:end-6), '_aligned map');
    folderName2 = [dname(5:11)];
    resultFolder2 = strcat(fname, '/', folderName2);
    resultFolder3 = strcat(fname, '/metaFiles');

    % Create directories if they don't exist
    if ~isfolder(resultFolder1)
        mkdir(resultFolder1);
    end
    if ~isfolder(resultFolder2)
        mkdir(resultFolder2);
    end
    if ~isfolder(resultFolder3)
        mkdir(resultFolder3);
    end

    % Get the full source file path
    sourceFile = fullfile(fname, dname);

    % Copy specific file types to analysis folder
    if strcmp(ext, '.segment') || strcmp(ext, '.align')
        if exist(sourceFile, 'file')
            copyfile(sourceFile, resultFolder1);
            fprintf('Copied %s to %s\n', dname, resultFolder1);
        else
            fprintf('Warning: File %s does not exist\n', sourceFile);
        end
    end

    % Move other data files to organized folder
    if strcmp(ext, '.signals')
        % For signals files, move to resultFolder2
        if exist(sourceFile, 'file')
            movefile(sourceFile, resultFolder2);
            fprintf('Moved %s to %s\n', dname, resultFolder2);
        else
            fprintf('Warning: File %s does not exist\n', sourceFile);
        end
    end

end
end