<html>

<p><h2>DownsampleReg</h2></p>

<p>A Matlab script for fast XY motion correction (image registration).</p>

<p>While the algorithm is very simple, empirically it has been found to outperform more sophisticated registration tools.</p>

<p>DownsampleReg performs particularly well on high-noise, low-signal datasets such as those produced in 2-photon imaging.</p>

<p><a href="files/downsampleReg.zip">Download</a></p>

<p><h3>Instructions:</h3></p>

<p>Extract the DownsampleReg files to a directory. Open Matlab, and change to that directory.</p>

<p>Before using your own images, it's a good idea to generate a series of test images and register them. To do this, type <b>makeTestSet</b>.</p>

<p><b>makeTestSet</b> will fill the "input" directory with images of a jittered cross mark. Run <b>downsampleReg</b> to generate the corrected images.</p>

<p>Open the "input" and "registered" images in ImageJ, and you will see:</p>

<img src="files/test_reg.gif" width="350px">

<p>Now, to perform registration on your own images, replace the images in the "input" directory with yours.</p>



<p><h3>Usage Tips:</h3></p>

<p>In the case of datasets with high noise and motion, try applying downsampleReg more than once. You can run downsampleReg on its own output to improve the registration.</p>

<table border=1 width="700px"><tr>
<td align="center"><img src="files/source_data.png" width="256px"><br />One frame of a noisy dataset.</td>
<td align="center"><img src="files/meanProj_unregistered.png" width="256px"><br />Mean projection of original imaging data.</td>
</tr>
<tr>
<td align="center"><img src="files/meanProj_registered_once.png" width="256px"><br />Mean projection of result when downsampleReg is run once.</td>

<td align="center"><img src="files/meanProj_registered_twice.png" width="256px"><br />Mean projection of result when downsampleReg is run again on its output.</td>
</tr></table>

<p>Preprocessing, such as median filtering or averaging consecutive frames together, may also improve registration results.</p>


<p><h3>About:</h3></p>
<p>downsampleReg v1.0 released May. 11, 2016.</p>

<p>Created by <a href="http://www.maxplanckflorida.org/fitzpatricklab/theo-walker.html">Theo Walker</a> (<a href="mailto:<EMAIL>">Email</a>).</p>

</html>