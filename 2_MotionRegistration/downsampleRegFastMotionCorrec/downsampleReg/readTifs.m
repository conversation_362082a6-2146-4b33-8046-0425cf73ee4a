function [imgStack, header] = readTifs(dirname)
imagefiles = dir([dirname '/*.tif']);      
nfiles = length(imagefiles);

%get image height and width from first file
firstImgPath = [dirname '/' imagefiles(1).name];
firstImg = imread(firstImgPath);
[h w] = size(firstImg);

%determine whether tif is multipage; use scanimage's reader if so.
s = imfinfo(firstImgPath);

imgStack = [];
header = [];

if size(s,1) == 1
    % Assume all files are 1-page tifs

    images = uint16(zeros(h,w,nfiles)); %change this to fit img resolution
    for ii=1:nfiles
       currentfilename = [dirname '/' imagefiles(ii).name];
       image = imread(currentfilename);
       images(:,:,ii) = image;
    end
    
    imgStack = images;
    
else
    for i = 1:nfiles
        % Read the multipage tifs using ScanImage's reader
        [header,aout] = opentif([dirname '/' imagefiles(i).name]); % use for SI5 and SI2015
        %[header,aout] = scim_openTif(files(i).name); use for SI4
        disp(['Loaded ' imagefiles(i).name]);
        aout = uint16(squeeze(aout(:,:,1,:)));
        imgStack = cat(3, imgStack, aout);
    end
    imgStack = (imgStack);

end
disp(['Finished loading ' num2str(size(imgStack,3)) ' images.']);


