function [ template_img ] = makeTemplate( imgStack )
% Returns the image with most unique autocorrelation peak.
% 
% [sizeY sizeX depth] = size(imgStack);

template_img = uint16(mean(imgStack, 3));
% 
% best_score = 0.0;
% best_index = 1;
% for d=1:depth
%     imgD = imgStack(:,:,d);
% 
%     grdX = abs(imgD(1:sizeY, 1:sizeX-1) - imgD(1:sizeY, 2:sizeX));
%     grdY = abs(imgD(1:sizeY-1, 1:sizeX) - imgD(2:sizeY, 1:sizeX));
%     
%     score = (sum(sum(grdX)) + sum(sum(grdY)));
%     %score = std2(imgD)/mean2(imgD);
%     if score > best_score
%         best_score = score;
%         best_index = d;
%     end
%     
% %     imgD(:,1:4) = score+10000;
% %     score
% %     imgStack(:,:,d) = imgD;
% end
% 
% % writeTifs(imgStack, './scored');
% template_img = imgStack(:,:,best_index);

end

