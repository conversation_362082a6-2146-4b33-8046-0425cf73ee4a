
% DAC = [0:16:256]';
DAC = linspace(0, 2 ,17)';
% DAC = 0    15    30    45    60    75    90   105   120   135   150   165   180   195   210   225   240   255

% LaserPower = [0.5	8.37	33.8	66.2	115.8	154.6	183.1	198.5	199.8	187.2	162.9	127.2	88.2	48	23.8	10.47	10.35]';
LaserPower = [3.1	 19.4	58.1	111.1	165.1	202.6	224.5	234	 230.8	214.4	185.2	141.5	88.4	47	20.5	13.8	13.8]';

figure;
plot(DAC, LaserPower,'o')
hold on

f = @(x,xdata) x(1).*sin(xdata*x(2)).^2
% pp = lsqcurvefit(f,[1500 0.01],DAC,LaserPower)
pp = lsqcurvefit(f,[1500 1],DAC,LaserPower)

hold on
xx = linspace(0,2,512);
plot(xx,f(pp,xx), 'g')



myfittype = fittype('A * (sin((pi * DAC) / (2 * V_pi)) .^ 2)','dependent',{'LaserPower'},'independent',{'DAC'}, 'coefficients',{'A', 'V_pi'});
myfit = fit(D<PERSON>,LaserPower,myfittype,'StartPoint', [150, 100]);

hold on
plot(myfit, DAC,LaserPower)

steps_powers = linspace(0, myfit.A, 256);
DAC_values = (0:round(myfit.V_pi));
laser_powers = myfit(DAC_values)';

pockels_lut = round(interp1(laser_powers, DAC_values, steps_powers,'linear', 'extrap'));

plot([0:255], steps_powers)

