2007/                                                                                               000700  003573  001761  00000000000 11634415101 012637  5                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         2007/europe05.m                                                                                     000700  003573  001761  00000004462 10452710042 014471  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         jan=[233.3	232.3	10.5	20.9	36.8	42.6	34.8	46.0	50.8	89.3	22.8	271.5	186.3	200.0	219.5	270.6	284.0	10.8	140.9	24.3	28.7	28.4	38.5	44.6	39.2	327.8	45.4	28.5	285.5	359.7	4.5];
feb=[205.1	222.8	215.3	211.7	219.4	250.4	257.4	235.7	231.9	240.5	323.5	195.7	192.1	214.3	264.3	216.0	117.2	58.5	70.8	78.4	78.5	230.8	247.3	250.4	233.8	205.1	196.1	63.5	-9999.0	-9999.0	-9999.0];
mar=[53.4	222.3	235.4	212.9	208.7	220.2	345.4	223.3	239.5	38.7	48.3	54.1	67.0	64.4	33.7	46.7	47.4	43.9	44.7	36.6	88.4	213.3	226.3	214.3	200.7	37.0	26.4	335.8	220.5	238.3	234.4];
apr=[251.0	231.6	234.1	237.7	222.9	241.7	287.5	26.9	197.3	198.5	130.6	57.9	110.4	27.0	228.1	242.5	268.0	180.2	41.6	48.9	166.4	204.8	250.8	323.6	232.3	236.9	242.4	75.3	62.4	51.5	-9999.0];
may=[66.1	82.3	98.7	59.8	62.2	74.3	76.2	80.2	58.8	25.6	29.9	226.5	42.7	316.3	211.2	222.5	202.7	212.5	225.6	244.7	228.9	188.8	235.3	61.8	286.1	235.5	227.6	252.5	246.1	238.4	251.3];
jun=[267.0	69.6	56.7	48.9	120.2	33.6	249.3	4.0	301.9	30.5	46.4	47.4	32.1	19.6	260.9	228.7	233.6	78.2	32.2	203.4	5.6	31.3	12.9	0.4	229.7	239.0	250.8	230.2	45.6	46.9	-9999.0];
jul=[36.8	34.2	132.4	142.6	130.6	102.3	226.4	214.5	16.1	344.5	208.8	219.0	226.7	235.2	216.8	220.4	131.2	50.9	34.1	103.5	59.0	62.2	50.9	330.3	45.2	27.5	33.0	245.4	250.7	252.7	242.6];
aug=[233.4	26.4	230.2	266.6	114.2	230.9	65.1	73.8	66.5	53.7	49.9	98.4	174.3	206.3	222.3	235.1	224.5	336.4	216.4	240.0	231.0	214.4	211.1	221.7	243.9	244.1	201.1	193.3	202.3	2.3	100.5];
sep=[34.4	283.9	247.9	218.5	234.9	260.4	227.8	231.5	245.0	250.2	241.6	208.9	240.7	213.4	213.2	216.2	223.8	225.8	230.8	234.2	229.0	258.1	237.0	224.9	239.2	235.1	229.6	217.4	216.3	213.2	-9999.0];
oct=[222.8	217.3	226.8	246.0	243.8	249.2	235.9	248.0	236.8	232.4	237.5	241.2	231.9	211.0	35.8	18.7	235.6	237.7	254.4	224.6	154.5	209.8	205.7	236.5	235.0	220.8	253.3	241.4	236.5	257.7	242.1];
nov=[250.4	225.4	242.1	226.7	232.5	250.6	308.3	113.5	205.2	221.9	220.8	225.7	219.2	203.0	41.3	200.8	216.0	33.3	198.1	213.8	229.6	228.7	232.0	239.2	239.1	58.7	186.3	44.1	24.1	140.6	-9999.0];
dec=[83.0	257.6	220.6	276.6	290.0	160.9	40.8	53.5	74.1	2.5	16.7	41.3	56.1	290.0	136.8	41.3	44.6	37.5	186.1	122.0	58.1	66.5	241.8	99.9	88.0	240.6	244.7	252.2	242.8	64.1	26.7];

angles=[jan feb mar apr may jun jul aug sep oct nov dec];
misspos=(angles==-9999);
totmiss=sum(misspos);
angles(misspos)=[];
angles=angles/360*2*pi; 

                                                                                                                                                                                                              2007/greenl05.m                                                                                     000700  003573  001761  00000004414 10452710042 014443  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         jan=[138.6	80.2	65.3	60.8	60.8	96.0	98.0	67.0	226.1	147.2	73.9	31.9	50.1	36.8	348.0	60.4	194.3	99.3	241.3	231.2	249.0	330.5	346.1	225.0	275.0	287.3	302.7	241.9	209.2	60.4	291.9];
feb=[9.8	242.1	232.7	271.7	263.5	244.5	63.6	220.7	217.3	218.7	219.6	113.1	22.6	7.7	233.3	183.9	235.1	248.7	281.1	269.6	244.6	242.8	208.0	23.0	0.6	233.6	167.4	238.6	-9999.0	-9999.0	-9999.0]; 
mar=[312.0	238.9	243.6	271.9	259.7	279.3	16.6	67.3	245.5	239.1	243.4	239.2	162.9	215.1	230.2	128.5	40.4	324.2	33.3	286.5	145.4	175.6	226.7	52.7	26.4	337.7	352.9	9.6	251.3	113.1	286.7];
apr=[26.8	308.2	229.1	230.3	223.7	224.9	264.9	232.2	45.3	242.1	231.1	228.9	223.4	47.9	86.3	71.3	56.9	83.9	71.3	0.9	231.9	240.4	229.9	221.7	229.7	208.3	238.6	58.4	222.1	246.1	-9999.0];
may=[226.4	60.2	39.5	225.0	89.2	10.0	258.2	249.0	64.0	231.1	258.3	224.9	243.9	240.9	241.6	231.4	227.2	218.2	227.1	177.3	229.1	216.2	218.1	210.3	17.0	14.6	266.3	189.9	257.4	52.5	49.7];
jun=[58.0	63.7	63.9	54.5	60.2	50.7	37.4	297.4	345.4	260.4	98.9	44.4	248.5	10.0	341.5	112.2	71.0	36.5	43.3	41.5	32.0	286.7	343.6	291.9	49.4	47.8	39.4	65.6	47.4	135.6	-9999.0]; 
jul=[57.5	44.9	63.3	353.3	48.6	253.1	88.2	17.3	250.0	223.4	234.5	237.2	238.4	233.9	276.4	227.1	45.6	351.3	343.5	268.7	43.1	286.7	241.1	237.8	49.9	295.9	219.9	53.0	47.1	43.7	55.2];
aug=[42.2	44.3	70.2	60.4	58.3	51.8	53.7	39.1	46.1	43.6	60.0	58.1	70.8	56.1	43.0	33.2	5.5	319.7	28.6	52.2	256.6	267.3	231.8	239.4	271.2	54.6	81.8	224.3	208.4	250.9	33.2];
sep=[245.3	252.2	56.4	229.1	130.6	313.4	232.9	224.6	288.7	312.8	8.1	41.4	224.8	247.0	4.7	234.4	217.6	180.3	164.7	99.0	87.9	121.3	65.9	84.7	55.6	8.2	33.7	84.6	46.4	40.4	-9999.0]; 
oct=[233.5	223.4	20.4	31.4	294.0	320.9	230.1	241.7	226.2	233.7	257.3	226.5	225.1	351.7	56.1	7.1	314.4	354.0	34.6	179.5	238.1	73.9	38.5	101.6	136.5	70.0	64.0	84.5	100.9	51.6	13.5];
nov=[281.4	284.1	95.9	71.5	51.3	302.6	83.3	62.8	38.8	41.4	151.6	30.3	226.9	187.7	41.6	338.9	287.5	20.5	16.7	3.1	219.8	209.5	207.0	271.7	249.7	240.3	242.4	114.3	258.2	62.8	-9999.0]; 
dec=[72.2	232.2	225.0	57.0	323.9	351.1	70.4	49.9	54.5	33.5	357.6	281.9	316.0	232.8	228.7	61.4	250.4	65.0	46.9	31.0	64.0	60.6	42.6	61.9	52.0	285.4	28.5	82.5	70.8	242.5	54.3];

angles=[jan feb mar apr may jun jul aug sep oct nov dec];
misspos=(angles==-9999);
totmiss=sum(misspos);
angles(misspos)=[];
angles=angles/360*2*pi; 

                                                                                                                                                                                                                                                    2007/gvmconst.m                                                                                     000700  003573  001761  00000000776 10452710042 014671  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         function g=gvmconst(d,k1,k2); 

%_____________________________________________________________________________
% GVMCONST.M
% arguments: d (m1-m2), k1, k2 
% This function computes the normalizing constant of the density of 
% GvM(m1,m2,k1,k2) 
%_____________________________________________________________________________

accuracy=10e-10;
summand=1;
sum=0;
j=1;

while summand>accuracy,
	summand=besseli(2*j,k1)*besseli(j,k2)*cos(2*j*d);
	sum=sum+summand;
	j=j+1;
end;

g=besseli(0,k1)*besseli(0,k2)+2*sum;
  2007/gvmises.m                                                                                      000700  003573  001761  00000000615 10452710042 014476  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         function f=gvmises(angles,m1,m2,k1,k2); 

%_____________________________________________________________________________
% GVMISES.M
% arguments: angles (abscissae),  m1, m2, k1, k2 
% This function computes the GvM(m1,m2,k1,k2) density 
%_____________________________________________________________________________

f=exp(k1*cos(angles-m1)+k2*cos(2*(angles-m2)))/(2*pi*gvmconst(m1-m2,k1,k2)); 

                                                                                                                   2007/mle.m                                                                                          000700  003573  001761  00000001265 10452710042 013600  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         %_____________________________________________________________________________________
% MLE.M
% angles (sample values) 
% This function computes the MLE under GvM(m1,m2,k1,k2)  
%_____________________________________________________________________________________

% options = optimset('MaxIter',1000,'MaxFunEvals',5000,'TolFun',1e-3,'TolX',1e-2);
options = [];

start=[0 0 0 0];

disp('fminsearch.m for log-likelihood running ...');
t = fminsearch('nloglik',start,options,angles); 
disp('... fminsearch.m done');

m1=mod(t(1),2*pi); m2=mod(t(2),2*pi); k1=exp(t(3)); k2=exp(t(4));
disp('mu1 = '), disp(m1);
disp('mu2 = '), disp(m2);
disp('kappa1 = '), disp(k1),
disp('kappa2 = '), disp(k2);
                                                                                                                                                                                                                                                                                                                                           2007/namerica05.m                                                                                   000700  003573  001761  00000004447 10452710042 014754  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         jan=[254.5	246.4	250.5	232.4	240.6	227.0	238.1	245.1	244.3	247.7	247.2	242.0	243.0	254.9	245.5	258.0	271.1	177.3	123.0	46.8	234.8	238.5	208.5	223.8	210.5	282.0	267.7	230.7	236.4	241.2	222.7];
feb=[245.1	221.0	246.5	271.5	322.5	237.1	232.7	220.3	217.5	236.2	171.1	75.6	277.2	255.9	246.8	246.1	243.3	252.5	244.9	236.3	249.1	282.5	350.9	69.5	43.5	46.8	53.0	45.8	-9999.0	-9999.0	-9999.0];
mar=[43.6	69.7	223.8	206.2	204.4	241.1	203.7	5.0	252.4	132.5	149.3	219.3	237.7	254.6	51.2	77.9	66.6	68.3	75.8	65.0	58.0	85.1	273.3	237.3	241.6	205.4	62.5	48.6	47.0	341.7	346.9];
apr=[8.7	250.8	58.5	71.2	61.1	53.3	61.0	70.3	66.1	193.9	76.3	68.7	71.3	87.8	244.3	220.2	315.6	276.5	228.8	239.4	226.6	312.1	228.5	55.7	82.5	76.4	62.4	53.0	52.0	40.1	-9999.0];
may=[183.0	230.5	242.1	229.8	123.7	61.1	53.8	45.6	54.7	52.9	271.9	226.0	68.8	60.4	64.3	62.0	66.1	63.4	63.5	67.4	53.3	46.9	63.1	66.2	332.5	56.3	52.0	55.8	45.9	36.4	118.3];
jun=[67.5	58.4	48.6	68.4	59.9	52.9	66.8	49.8	41.9	43.8	62.9	66.0	61.7	32.1	357.3	47.7	55.7	204.2	237.8	247.1	276.2	228.5	239.8	218.4	70.7	241.4	222.1	243.2	249.7	-9999.0];
jul=[240.4	226.0	270.3	247.3	231.0	271.0	246.2	230.1	238.2	241.1	229.6	234.8	273.0	245.5	247.2	94.7	254.3	269.6	224.5	243.2	250.0	260.4	310.8	231.4	228.7	242.4	272.2	226.0	274.3	249.9	249.5];
aug=[240.2	282.0	319.7	272.7	241.1	232.1	228.7	237.9	235.0	245.2	230.3	233.5	254.3	211.2	228.4	227.4	185.2	102.2	241.8	237.1	225.8	247.4	251.3	254.4	236.1	240.6	242.8	60.8	44.2	0.9	227.5];
sep=[228.5	141.1	264.8	252.1	258.0	249.0	241.5	255.0	11.7	228.2	254.0	237.5	225.2	247.6	268.1	228.0	25.8	177.5	161.4	249.3	256.4	245.7	13.5	234.2	236.0	248.4	181.1	117.7	357.5	255.0	-9999.0];
oct=[109.5	38.5	174.0	29.2	25.3	47.1	34.8	18.4	69.7	88.0	60.2	89.6	302.8	275.2	59.3	48.7	337.1	240.4	326.7	7.7	19.4	42.1	18.8	204.5	43.7	154.7	227.6	213.6	269.7	255.5	232.9];
nov=[41.2	90.5	36.7	50.7	64.7	67.7	41.2	37.9	54.1	74.7	5.6	332.0	230.4	228.8	251.9	252.7	236.7	242.4	238.2	248.3	255.1	235.2	153.0	46.2	96.9	237.5	239.3	227.6	220.6	234.0	-9999.0];
dec=[233.5	232.2	36.6	312.9	233.4	230.2	226.9	227.5	252.8	237.5	254.4	241.4	245.9	186.5	62.7	45.5	206.8	238.8	236.1	256.5	232.8	179.2	62.5	38.8	39.0	52.5	56.4	56.7	56.6	59.0	59.6];

angles=[jan feb mar apr may jun jul aug sep oct nov dec];
misspos=(angles==-9999);
totmiss=sum(misspos);
angles(misspos)=[];
angles=angles/360*2*pi; 

                                                                                                                                                                                                                         2007/nlik.m                                                                                         000700  003573  001761  00000001175 10452710042 013760  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         function nl = nlik(t,angles); 

%________________________________________________________________________________________
% NLIK.M
% arguments: t(1) (m1), t(2) (m2), t(3) (l1), t(4) (l2), angles (sample values) 
% This function computes -1 X the likelihood under GvM(m1,m2,e^l1,e^l2) up to a   
% multiplicative constant. Note that the concentrations are in logarithm  
%________________________________________________________________________________________

m1 = t(1); m2 = t(2); k1 = exp(t(3)); k2 = exp(t(4));
n = length(angles);
l = exp( k1*sum(cos(angles-m1)) + k2*sum(cos(2*(angles-m2))) )/(gvmconst(m1-m2,k1,k2)^n); 
nl = -l;


                                                                                                                                                                                                                                                                                                                                                                                                   2007/nloglik.m                                                                                      000700  003573  001761  00000001213 10452710042 014453  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         function nl = nloglik(t,angles); 

%________________________________________________________________________________________
% arguments: t(1) (m1), t(2) (m2), t(3) (l1), t(4) (l2), angles (sample values)
% This function computes -1 X the log likelihood under GvM(m1,m2,e^l1,e^l2) up to a
% multiplicative constant. Note that the two last variables are logarithms of 
% concentrations 
%________________________________________________________________________________________

m1 = t(1); m2 = t(2); k1 = exp(t(3)); k2 = exp(t(4));
n = length(angles);
l = k1*sum(cos(angles-m1)) + k2*sum(cos(2*(angles-m2))) - n*log(gvmconst(m1-m2,k1,k2)); 
nl = -l;


                                                                                                                                                                                                                                                                                                                                                                                     2007/panarct05.m                                                                                    000700  003573  001761  00000004557 10452710042 014627  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         jan=[241.4	233.7	240.6	236.6	241.6	240.3	242.8	244.6	249.7	255.7	247.2	250.5	226.1	238.8	234.4	238.0	232.2	229.7	216.4	322.2	243.0	247.3	237.3	231.8	237.2	260.1	250.1	237.1	237.7	248.4	237.6];
feb=[244.4	221.2	242.3	245.5	247.6	243.3	244.3	230.7	230.4	236.7	232.3	179.3	238.2	242.2	239.4	237.0	242.1	255.5	252.8	226.9	251.1	257.1	248.2	241.7	242.8	207.4	213.9	240.9	-9999.0	-9999.0	-9999.0];
mar=[356.2	221.1	229.5	240.7	236.6	237.6	211.9	250.3	250.9	201.6	227.5	229.3	242.9	238.7	290.0	234.8	238.2	39.0	88.8	54.1	59.7	169.8	241.8	243.1	232.1	176.6	96.2	11.4	262.8	270.9	232.4];
apr=[235.0	242.1	224.6	221.4	135.4	243.0	209.7	242.2	228.8	226.5	205.1	90.1	84.1	51.6	255.3	215.5	35.4	43.2	221.3	236.9	226.4	251.5	242.1	254.9	168.4	109.8	291.6	33.9	70.7	36.3	-9999.0];
may=[76.5	80.3	19.3	191.3	69.3	59.5	58.4	52.5	60.0	50.0	11.2	223.3	214.4	71.3	160.7	208.7	198.1	225.7	225.3	217.8	235.4	247.3	244.2	228.5	254.4	99.9	56.8	26.9	345.5	29.1	101.6];
jun=[39.4	67.1	49.4	43.8	58.9	49.7	39.1	56.7	50.2	49.8	54.6	71.7	61.9	36.3	277.9	242.9	310.2	46.6	138.2	243.7	355.8	3.1	352.4	294.6	219.9	221.2	237.5	229.0	227.9	55.4	-9999.0];
jul=[54.2	71.7	122.7	292.1	234.3	259.6	233.2	230.9	253.6	245.5	229.9	236.7	253.9	251.7	220.4	186.1	232.3	243.0	214.9	238.3	304.2	35.6	2.6	237.0	43.4	274.8	311.1	222.1	290.4	273.5	238.1];
aug=[216.0	257.7	263.1	251.8	245.4	229.2	250.8	275.0	244.5	345.1	226.6	234.8	233.4	225.2	230.7	255.0	234.5	232.6	233.4	244.7	233.8	233.7	243.9	239.4	240.8	241.4	249.1	222.7	140.9	71.3	94.1];
sep=[88.0	254.0	280.8	255.9	249.0	241.9	238.7	245.7	236.8	234.5	234.2	237.8	237.6	235.2	237.3	224.9	225.9	221.2	223.5	234.0	235.4	238.3	229.0	228.5	225.0	245.1	234.5	232.0	248.0	251.2	-9999.0];
oct=[237.0	242.0	222.6	240.1	296.0	249.6	232.7	240.8	234.9	229.3	237.6	233.7	251.2	252.5	248.4	249.9	252.6	242.8	260.0	232.9	247.1	322.7	243.6	221.0	217.6	232.4	238.9	222.2	233.8	247.2	250.4];
nov=[251.6	226.4	266.1	234.8	253.0	47.7	45.6	35.5	194.3	194.6	222.0	232.9	224.1	213.7	252.6	227.9	229.8	242.6	234.3	245.8	244.7	237.1	229.2	240.5	242.8	240.3	232.3	241.4	233.4	239.8	-9999.0];
dec=[334.3	30.9	53.7	53.3	232.3	226.0	238.0	226.9	248.5	250.4	264.9	268.1	326.3	297.4	96.5	54.1	245.8	267.4	232.9	269.7	7.5	200.0	244.0	120.8	56.4	318.2	255.1	235.0	229.2	197.4	127.4];

angles=[jan feb mar apr may jun jul aug sep oct nov dec];
misspos=(angles==-9999);
totmiss=sum(misspos);
angles(misspos)=[];
angles=angles/360*2*pi; 

                                                                                                                                                 2007/Readme.txt                                                                                     000700  003573  001761  00000001417 11634415101 014603  0                                                                                                    ustar 00gatto                           CAMPUS\stat                     000000  000000                                                                                                                                                                         
This directory contains the data and the Matlab programs of the article 
Gatto and Jammalamadaka (2007), ``The generalized von Mises distribution'',
Statistical Methodology, 4, 341-353.

The data were sampled by the ArcticRIMS Project http://rims.unh.edu 

They are wind direction measurements, daily on continental level, from 
January to December 2005
http://rims.unh.edu/data/data.cgi?space=cont&time=daily&category=3&subject=3

There are 4 data sets which can be read with the following Matlab files
Pan Arctic basin: panarct05.m
Europe basin: europe05.m  
Greenland basin: greenl05.m
North America basin: namerica05.m

The Matlab programs allow to compute the GvM density, the likelihood function
and the maximum likelihood estimators. They can be found in the remaining
files
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 