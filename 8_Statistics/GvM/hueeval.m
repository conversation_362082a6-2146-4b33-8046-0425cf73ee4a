function sse = hueeval(t,hueAngle,hueResp)

%________________________________________________________________________________________
% arguments: t(1) (m1), t(2) (m2), t(3) (l1), t(4) (l2), hue angles and hueResp(sample values)
%________________________________________________________________________________________

m1 = t(1); m2 = t(2); k1 = exp(t(3)); k2 = exp(t(4));
% n = length(hueAngle);
f = exp(k1*cos(hueAngle-m1)+k2*cos(2*(hueAngle-m2)))/(2*pi*gvmconst(m1-m2,k1,k2));
% f = exp(k1*cos(hueAngle-m1)+k2*cos(2*(hueAngle-m2)));
sse = sum(abs(hueResp-f)); 
