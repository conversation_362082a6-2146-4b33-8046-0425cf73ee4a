function nl = nloglik(t,angles); 

%________________________________________________________________________________________
% arguments: t(1) (m1), t(2) (m2), t(3) (l1), t(4) (l2), angles (sample values)
% This function computes -1 X the log likelihood under GvM(m1,m2,e^l1,e^l2) up to a
% multiplicative constant. Note that the two last variables are logarithms of 
% concentrations 
%________________________________________________________________________________________

m1 = t(1); m2 = t(2); k1 = exp(t(3)); k2 = exp(t(4));
n = length(angles);
l = k1*sum(cos(angles-m1)) + k2*sum(cos(2*(angles-m2))) - n*log(gvmconst(m1-m2,k1,k2)); 
nl = -l;


