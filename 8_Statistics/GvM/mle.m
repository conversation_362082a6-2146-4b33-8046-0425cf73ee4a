%_____________________________________________________________________________________
% MLE.M
% angles (sample values) 
% This function computes the MLE under GvM(m1,m2,k1,k2)  
%_____________________________________________________________________________________
fun = @(t)hueeval(t,hueAngle,hueResp);
% options = optimset('MaxIter',1000,'MaxFunEvals',5000,'TolFun',1e-3,'TolX',1e-2);
options = [];

start=[0 0 0 0];

disp('fminsearch.m for log-likelihood running ...');
% t = fminsearch('nloglik',start,options,angles); 
t = fminsearch(fun,start);
disp('... fminsearch.m done');

m1=mod(t(1),2*pi); m2=mod(t(2),2*pi); k1=exp(t(3)); k2=exp(t(4));
disp('mu1 = '), disp(m1);
disp('mu2 = '), disp(m2);
disp('kappa1 = '), disp(k1),
disp('kappa2 = '), disp(k2);
