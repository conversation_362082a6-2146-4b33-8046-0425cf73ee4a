function g=gvmconst(d,k1,k2); 

%_____________________________________________________________________________
% GVMCONST.M
% arguments: d (m1-m2), k1, k2 
% This function computes the normalizing constant of the density of 
% GvM(m1,m2,k1,k2) 
%_____________________________________________________________________________

accuracy=10e-10;
summand=1;
sum=0;
j=1;

while summand>accuracy,
	summand=besseli(2*j,k1)*besseli(j,k2)*cos(2*j*d);
	sum=sum+summand;
	j=j+1;
end;

g=besseli(0,k1)*besseli(0,k2)+2*sum;
