function nl = nlik(t,angles); 

%________________________________________________________________________________________
% NLIK.M
% arguments: t(1) (m1), t(2) (m2), t(3) (l1), t(4) (l2), angles (sample values) 
% This function computes -1 X the likelihood under GvM(m1,m2,e^l1,e^l2) up to a   
% multiplicative constant. Note that the concentrations are in logarithm  
%________________________________________________________________________________________

m1 = t(1); m2 = t(2); k1 = exp(t(3)); k2 = exp(t(4));
n = length(angles);
l = exp( k1*sum(cos(angles-m1)) + k2*sum(cos(2*(angles-m2))) )/(gvmconst(m1-m2,k1,k2)^n); 
nl = -l;


