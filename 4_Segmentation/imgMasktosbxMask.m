clear;
clc;
animal = 'AG4';
unit = '007';
plane = '001';
dataDrive = '/media/vcl/VCL004';
minSize = 1; % minimal height (in pixel) of a cell/region, eg =2 means minimal is 2x2=4 pixels
%% Part I Count and get the border of each cell and generate sbx mask(each cell ROI has the value of the cell's number)

dataRoot = strcat(dataDrive, '/', animal,'/2P_data', '/U', unit, '/');
% dataFiles = dir(strcat(dataRoot, 'mask\Final mask_', unit, '_', plane,'\*.png'));
dataFiles = dir(strcat(dataRoot, 'mask/Final mask_', animal, '_U', unit, '_plane', plane,'/*.png'));
% dataFiles = dir(strcat(dataRoot, 'mask\Final mask_', animal, '_U',unit, '\*.png'));

% dataRoot = strcat('O:\AF4\ISI_analysis\1. Image segmentation\3. Response mask\MakeMask\DomainMask\DomainSeg\');
% dataFiles = dir(strcat(dataRoot, '*.tif'));

numFiles = size(dataFiles, 1);
cd(dataFiles(1).folder)

for i = 1:numFiles
    fileName = dataFiles(i).name
    
    resultName = strcat(animal, '_', unit, '_', fileName(1:3), '_ot_', plane);
%     resultName = strcat(animal, '_', unit, '_', fileName(1:3));
%     resultName = fileName(1:end-4);
    
    % load mask
    maskApproved = logical(imread(fileName));
    if size(maskApproved,3) == 4  % remove alpha layer if there is
        maskApproved = mean(maskApproved(:,:,1:3),3);
    else
        maskApproved = mean(maskApproved,3);
    end
        
    % extract border and region
    [vert, mask] = findholesPL(maskApproved, minSize);
    
    % Save results
    save([dataRoot, resultName, '.segment'],'mask','vert');
    fprintf('Saved segments\n');
end



