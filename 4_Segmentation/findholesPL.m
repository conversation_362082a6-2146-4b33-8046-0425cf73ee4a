function [vert, mask] = findholesPL(maskApproved, thres)

% Peichao: this code can find the border (vert) and the region (mask) of holes 

% make sure borders are 0s
maskApproved(1,:) = 0;
maskApproved(end,:) = 0;
maskApproved(:,1) = 0;
maskApproved(:,end) = 0;

maskApproved = logical(maskApproved);
mask = zeros(size(maskApproved));
canvas = maskApproved;
templet = canvas;   % White hole
templet2 = logical(1-canvas);  % Black holes
cellNum = 0;
repeat=1;
while repeat
    [y, x]=find(templet, 1); % PL find the first domain (white holes in the mask) location. y (row) and x(column) are the coordinate of the domain.
     if isempty(y)
        break;
     else
        templet2 = imfill(templet2,[y,x]); 
        templet3 = templet-1+templet2;
        [a,b] = find(templet3==1);
        border = bwperim(templet3);
        [row,col]= find(border==1); % get the coordinates
%         if length(row) < 2
%             warning("Single Pixel exists in Mask, Please check!!!")
%         end
        templet=logical(1-templet2);
    %     figure
    %     imshow(templet3)
     end
     if length(row) >= thres
        cellNum = cellNum+1;
        vert{cellNum} = [col row]; %To match with xy notion of plotting figures, which means x is column number, y is row number. PL
        indices = sub2ind(size(mask), a, b);
        mask(indices) = cellNum;
     else  % escape signel pixel
        warning("Single Pixel exists in Mask, Please check!!!")
        warning("This pixel is not considered as a cell!!!")
    end

end
end