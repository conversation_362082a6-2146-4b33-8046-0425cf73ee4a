clear;
animal = 'AE7';
unit = '006';
% plane = '000';
dataDrive = 'G:';

%% Part I Count and get the border of each cell and generate sbx mask(each cell ROI has the value of the cell's number)

dataRoot = strcat('C:\Users\<USER>\Desktop\Ne\');
% dataFiles = dir(strcat(dataRoot, 'mask\Final mask_', animal, '_U', unit, '_plane', plane,'\*.png'));
% dataFiles = dir(strcat(dataRoot, 'mask\Final mask_', animal, '_U',unit, '\*.png'));
cd(dataRoot)
dataFiles = dir(strcat(dataRoot, 'Layer1.bmp'));

numFiles = size(dataFiles, 1);
cd(dataFiles(1).folder)

% for i = 1:numFiles
i = 1;
dataFiles(i).name

%     fileName = strcat(animal, '_', unit, '_', dataFiles(i).name(1:3), '_ot_', plane);
% fileName = strcat(animal, '_', unit, '_', dataFiles(i).name(1:3));
maskApproved = imread(dataFiles(1).name);
maskApproved = logical(mean(maskApproved,3));
mask = zeros(size(maskApproved));
canvas = maskApproved;
templet = canvas;   % White hole
templet2 = logical(1-canvas);  % Black holes
cellNum = 0;
repeat=1;
while repeat
    [y, x]=find(templet, 1); % PL find the first domain (white holes in the mask) location. y (row) and x(column) are the coordinate of the domain.
     if isempty(y)
        break;
     else
        templet2 = imfill(templet2,[y,x]); 
        templet3 = templet-1+templet2;
%         [a,b] = find(templet3==1);
        border = bwperim(templet3);
        [row,col]= find(border==1); % get the coordinates
        
        if length(row) < 2
            warning("Single Pixel exists in Mask, Please check!!!")
        end
        templet=logical(1-templet2);
    %     figure
    %     imshow(templet3)
     end
     
%      cellNum = cellNum+1;
%      vert{cellNum} = [col row]; %To match with xy notion of plotting figures, which means x is column number, y is row number. PL
%      indices = sub2ind(size(mask), a, b);
%      mask(indices) = cellNum;
end

writeImageJROI([row,col], 2, 0,0,0, dataRoot)


% Save results
save([dataRoot, fileName, '.segment'],'mask','vert');
fprintf('Saved segments\n');
% end