% Peichao: This for double checking whether the segment files are correct!
% .segment file to image mask (white holes on black background)

clear;
clc;
animal = 'AF4';
% unit = '001';
% plane = '000';
dataDrive = 'O:';


%%
% dataRoot = strcat(dataDrive, '\', animal,'\2P_data', '\U', unit, '\');
% dataFiles = dir(strcat(dataRoot, 'mask\Final mask_', animal, '_U', unit, '_plane', plane,'\*.png'));
% % dataFiles = dir(strcat(dataRoot, 'mask\Final mask_', animal, '_U',unit, '\*.png'));

dataRoot = strcat('O:\AF4\2P_analysis\U004\_Summary\Multiplane\2. Correlation of Cone weights based on ISI\mask\');


dataFiles = dir(strcat(dataRoot, '**','\*.segment'));

% dataFiles = dataFiles(47:48);


numFiles = size(dataFiles, 1);

% resultFolder = strcat(dataFiles(1).folder, '\Mask_check', '\');
% 
% if ~isfolder(resultFolder)
%     mkdir(resultFolder);
% end 



for i=1:numFiles

    dname = dataFiles(i).name
    cd(dataFiles(i).folder)
    
    load(dname, '-mat')

    folderName = [dname(1:7), '_check'];
    maskName = strcat(dname(1:end-8), '_maskcheck.tif');
    
    % unit folder
    unit = dname(5:7);
    unitFolder = strcat(dataRoot, 'U', unit, '\');
%     if ~isfolder(unitFolder)
%         mkdir(unitFolder);    
%     end

    resultFolderRoot = strcat(unitFolder, 'mask\');
    resultFolder = strcat(resultFolderRoot, folderName, '\');
    
    if ~isfolder(resultFolder)
        mkdir(resultFolder);    
    end
    
    imwrite(mask, [resultFolder,maskName]);

end

