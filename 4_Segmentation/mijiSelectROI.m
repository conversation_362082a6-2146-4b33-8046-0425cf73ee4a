
% Peichao Li
% 0. .npy is generated by cellpose, which is a cell probability map.
% 1. This code is for transforming .npy matrix to images with local contrast enhancement as optional.
% 2. Next, image is loaded into imageJ for manually segmentation using magic cell wand tool packages in imageJ

%% Run this session first. Load iamge
dataFolder = 'C:\Users\<USER>\Desktop\AG4_mask-1\001_plane000\';
fileName = 'AG4_001_000_ot_000_aligned.zip';
ImageName = 'AG4_001_004_ot_000_aligned map_enhanced';
cd(dataFolder)
ReferenceImage = imread(fullfile(dataFolder, [ImageName, '.tif']));
% ReferenceImage = readNPY(fullfile(dataFolder, [ImageName, '.npy']));
% ReferenceImage = double(mat2gray(mean(ReferenceImage,3)));
% imwrite(norm_to_uint8(ReferenceImage),[dataFolder, ImageName, '_1.tif'])
% ReferenceImage = adapthisteq(ReferenceImage,'NumTiles',[16 16],'Distribution','Exponential');
% imwrite(norm_to_uint8(ReferenceImage),[dataFolder, ImageName, '_enhanced.tif'])

imageSize = size(ReferenceImage);

%%  Draw ROIs by ImageJ (magic cell wand tool) and save the ROIs for manually checking

% Add ImageJ to working directory
ImageJ_Path   = 'C:\Users\<USER>\Dropbox\Github\2P\1_ImageJ_Matlab\Fiji.app\';
addpath([ImageJ_Path '\scripts\']);
javaaddpath([matlabroot '\java\mij.jar']);
javaaddpath([matlabroot '\java\ij.jar']);
% intializeMIJ

Miji

RM = ij.plugin.frame.RoiManager();
RC = RM.getInstance();

%% Open ROIs as image to manually check, then save the approved mask to transform

RC.runCommand('Open', strrep([dataFolder, fileName],'\','\\')); 
% neuropilROI = generateNeuropilROIs(RC.getRoisAsArray); % Generates neuropil ROIs - Adds an an additional 199 ROIs
% RC.runCommand('Open', neuropilROI);

labeledROI = createLabeledROIFromImageJPixels(imageSize,RC.getRoisAsArray);

% Check image if there ROIs are jointed together. If yes, seperate them
% manually, and save it as binary bmp file named 'mask_approved'
imshow(labeledROI);
imwrite(logical(labeledROI(1:imageSize(1), 1:imageSize(2))), [dataFolder, ImageName, '_mask.tif']);


%% Random color fill to check conjuction of different ROIs by eye
maskName = 'AG4_001_004_ot_000_aligned map_enhanced_mask';
cd(dataFolder)

img = imread([dataFolder, maskName, '.bmp']);
[B,L] = bwboundaries(img,'noholes');
imshow(label2rgb(L, @lines, [.5 .5 .5]))
% dataFolder = 'C:\Users\<USER>\Desktop\Make mask\AF3_make mask\008_plane001\';

% hold on
% for k = 1:length(B)
%    boundary = B{k};
%    plot(boundary(:,2), boundary(:,1), 'w', 'LineWidth', 0.5)
% end