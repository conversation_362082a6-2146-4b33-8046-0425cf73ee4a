% Peichao: This is for data have single plane

% Notes:
% 1. Maybe try filtering after TTest
%

%% User Inputs
% Expt info.
animal = 'AG4';
unitFisrt = 8;  %  first unit in the main folder
unitEnd = 9;   % last unit in the main folder
dataDrive = '/media/peichao/New Volume/';  %

for j = unitFisrt:unitEnd
     
    unit = strcat('00', num2str(j))
    
    % main folder
%     mainFolder = strcat(dataDrive, animal, '\2P_data\');
    mainFolder = '/media/peichao/New Volume/AG4/2P_data/';
    cd(mainFolder)
    dataFiles1 = dir(strcat(animal, '_', unit, '*'));

    % unit folder
    unitFolder = strcat(mainFolder, 'U', unit, '/');
    if ~isfolder(unitFolder)
        mkdir(unitFolder);    
    end

    %move data files into the folder
    for i = 1:size(dataFiles1, 1)
        movefile(dataFiles1(i).name, unitFolder);
    end
    cd(unitFolder)
    resultFolderRoot = strcat(unitFolder, 'mask/');

    %% Read, filter & reorganize data
    dataFiles = dir('**/*.align');

    numFiles = size(dataFiles, 1);

    for i = 1:numFiles
        dataFiles(i).name
        cd(dataFiles(i).folder)
        load(dataFiles(i).name, '-mat')

        plane = dataFiles(i).name(16:18);
        folderName = [dataFiles(i).name(5:7), '_plane', plane];
        folderName1 = [dataFiles(i).name(5:11), '_', plane];
        mapName = strcat(dataFiles(i).name(1:end-6), '_aligned map');

        resultFolder = strcat(resultFolderRoot, folderName, '/');
        resultFolder1 = strcat(dataDrive, animal, '/2P_analysis/', 'U', unit, '/', folderName1, '/DataExport/' );
        if ~isfolder(resultFolder)
            mkdir(resultFolder);    
        end
        if ~isfolder(resultFolder1)
            mkdir(resultFolder1);    
        end

        imwrite(norm_to_uint8(m), [resultFolder, mapName,'.tif'])
        % Copy data files into the analysis folder
        copyfile(dataFiles(i).name, resultFolder1);

    end
end   