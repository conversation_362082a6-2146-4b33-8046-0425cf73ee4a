clc
clear
% animal = 'AE7';
% unit = '001';
% plane = '000';
% Drive = 'H:';
inferSpikes = 0;  % 0, do not infer spike; 1: infer spikes
multiplane = 1; % 0, single plane scanning; 1: multiple plane scanning, I usually do 2 planes.
% dataRoot = strcat(Drive, '\', animal,'\2P_data', '\U', unit, '\');

dataRoot = '/media/vcl/VCL004/AG4/2P_data/U007/';

% folder = dir(dataRoot);
% subFolder = folder([folder.isdir]);
% numFiles = size(subFolder, 1);
% dataFiles = dir(strcat(dataRoot, '*', plane,'.segment'));
dataFiles = dir(strcat(dataRoot, '*.segment'));
numFiles = size(dataFiles, 1);
cd(dataFiles(1).folder)

for i = 1:numFiles
    dataFiles(i).name
    fname = dataFiles(i).name(1:end-8);
    if inferSpikes == 1
        sbxpullsignalsSpike(fname,multiplane);
    elseif inferSpikes == 0
        sbxpullsignalsNospike(fname,multiplane);
    end
end


    
