function [sig,np,spks]= sbxpullsignalsSpike(fname,multiplane)

if multiplane == 1
    scanName = [fname(1:15),'parse.scan'];
    aligned = load([fname '*.align'], '-mat');
elseif multiplane == 0
    scanName = [fname,'_parse.scan'];
    aligned = load([fname '.align'], '-mat');
end
    
load([fname '.segment'],'-mat'); % load segmentation
load(scanName, '-mat'); % load info here, I don't like to use global. This info should be parsed first.


if multiplane == 1   % multiplane
    nameplane = str2double(fname(16:18));
    idxplane = nameplane+1;

    info.(sprintf('alignpl%d', nameplane)) = aligned;
    nframe = info.frNumPlane(idxplane);

elseif multiplane == 0    % sigle plane
    nframe = info.totalFrame;
    info.alignpl0 = aligned;
end

if(~exist('np_mask','var'))    % if does not have neuropil mask
    
    np = [];
    
%     z = sbxread(fname,1,1);
%     
%     global info;
    
    ncell = max(mask(:));
    nchan = info.nchan;
    
    for(i=1:ncell)
        idx{i} = find(mask==i);
    end
    
    sig = zeros(nchan,nframe, ncell);
    info.fid = fopen([fname '.sbx']);
    h = waitbar(0,sprintf('Pulling %d signals out...',ncell));

    for(i=0:nframe-1)
        waitbar(i/(nframe-1),h);          % update waitbar...
        z = sbxread(info,i,1);                 % Read in one frame/image
        % z = squeeze(z(1,:,:));
        z = circshift(z,aligned.T(i+1,:)); % align the image
        for(j=1:ncell)                             % for each cell
            for k = 1:nchan
                q = squeeze(z(k,:,:));
                sig(k,i+1,j) = mean(q(idx{j}));    % pull the mean signal out...
            end
        end
    end
    
    spks = zeros(size(sig));
    for i = 1:ncell
        for(k = 1:nchan)
            % spks(:,i) = deconv(sig(:,i), [0.33    1.8503    0.2958    9.3894]);
            spks(k,:,i) = deconv(squeeze(sig(k,:,i))', [1.5    1.8503    0.2958    9.3894]);
        end
    end
    
    sig = squeeze(sig); % drop one channel if not present...
    spks = squeeze(spks);
    
    delete(h);
    
    save([fname '.signals'],'sig', 'spks');     % append the motion estimate data...
    save(scanName, 'info');
else
    
%     z = sbxread(fname,1,1);
%     
%     global info;
    
    ncell = max(mask(:));
    nchan = size(z,1);

    for(i=1:ncell)
        idx{i} = find(mask==i);
        np_idx{i} = find(np_mask{i}>0);
    end
    
    sig = zeros(nchan,nframe, ncell);
    np = zeros(size(sig));
    spks = zeros(size(sig));
    info.fid = fopen([fname '.sbx']);
    h = waitbar(0,sprintf('Pulling %d signals/neuropil out...',ncell));
    
    for(i=0:nframe-1)
        waitbar(i/(nframe-1),h);          % update waitbar...
        z = sbxread(info,i,1);
        z = squeeze(z(1,:,:));
        z = circshift(z,aligned.T(i+1,:)); % align the image
        for(j=1:ncell)                          % for each cell
            for k=1:nchan
                q = squeeze(z(k,:,:));
                sig(k,i+1,j) = mean(q(idx{j}));       % pull the mean signal out...
                np(k,i+1,j) = median(q(np_idx{j}));
            end
        end
    end
    
    for i = 1:ncell
        for(k = 1:nchan)
            % spks(:,i) = deconv(sig(:,i), [0.33    1.8503    0.2958    9.3894]);
            spks(k,:,i) = deconv(squeeze(sig(k,:,i))', [1.5    1.8503    0.2958    9.3894]);
        end
    end
                
    sig = squeeze(sig); % drop one channel if not present...
    np = squeeze(np); % drop one channel if not present...
    spks = squeeze(spks);

    delete(h);
     
    save([fname '.signals'],'sig','np','spks');     % append the motion estimate data...
    save(scanName, 'info');
end


function y = deconv(y,x)

s = x(1);   % sigma
th = x(2);  % theta
b = x(3);   % beta
a = x(4);   % alpha

nsamp = size(y,1);

% Odd filter

t = 0:nsamp-1;
w = t.*exp(-t.^2 / (2*s^2));
w(2:end) = (w(2:end)-w(end:-1:2));
w = -w';
w = w/norm(w);

% Even filter

w0 = zeros(nsamp,1);
w0 = exp(-t.^2 / (2*s^2));
w0(2:end) = (w0(2:end)+w0(end:-1:2));
w0 = w0';
w0 = w0/norm(w0);
 
% Filtered signals

wf0 = fft(w0);
xf0 = real(ifft(fft(y(1:nsamp)).*wf0));
xf0 = zscore(xf0);

wf = fft(w);
xf = real(ifft(fft(y(1:nsamp)).*wf));
xf = zscore(xf);

% Of course one can combine the filters first and convolve once...
% but for historical reasons I kept them separate.

% Linear combination of filtered signals

xf = cosd(a)*xf+sind(a)*xf0;

% Output nonlinearity

y(1:nsamp) = (xf-th).^b .* (xf>=th);



