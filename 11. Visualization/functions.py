import pandas as pd
import numpy as np
import scipy.io as sio
from shapely.geometry.polygon import Polygon
from descartes import PolygonPatch

def load_analyzer(analyzer_path):
    analyzer_complete = sio.loadmat(analyzer_path, squeeze_me=True, struct_as_record=False)
    analyzer = analyzer_complete['Analyzer']
    return analyzer


# def load_analyzer(analyzer_path):
#     analyzer_complete = sio.loadmat(analyzer_path, squeeze_me=True, struct_as_record=False)
#     analyzer = analyzer_complete['Analyzer']
#     return analyzer


def analyzer_pg(analyzer_path):
    analyzer = load_analyzer(analyzer_path)

    b_flag = 0
    if type(analyzer.loops.conds[len(analyzer.loops.conds) - 1].symbol) != str:
        if analyzer.loops.conds[len(analyzer.loops.conds) - 1].symbol[0] == 'blank':
            b_flag = 1
    else:
        if analyzer.loops.conds[len(analyzer.loops.conds) - 1].symbol == 'blank':
            b_flag = 1

    if b_flag == 0:
        if type(analyzer.loops.conds[0].symbol) != str:
            trial_num = np.zeros((len(analyzer.loops.conds) * len(analyzer.loops.conds[0].repeats),
                                 (len(analyzer.loops.conds[0].symbol))))
        else:
            trial_num = np.zeros((len(analyzer.loops.conds) * len(analyzer.loops.conds[0].repeats), 1))
    else:
        if type(analyzer.loops.conds[0].symbol) != str:
            trial_num = np.zeros(((len(analyzer.loops.conds) - b_flag) * len(analyzer.loops.conds[0].repeats) +
                                  len(analyzer.loops.conds[-1].repeats),
                                  (len(analyzer.loops.conds[0].symbol))))
        else:
            trial_num = np.zeros(((len(analyzer.loops.conds) - b_flag) * len(analyzer.loops.conds[0].repeats) +
                                  len(analyzer.loops.conds[-1].repeats), 1))

    for count in range(0, len(analyzer.loops.conds) - b_flag):
        if type(analyzer.loops.conds[count].symbol) != str:
            trial_vals = np.zeros(len(analyzer.loops.conds[count].symbol))
        else:
            trial_vals = np.zeros(1)

        try:
            for count2 in range(0, len(analyzer.loops.conds[count].symbol)):
                trial_vals[count2] = analyzer.loops.conds[count].val[count2]
        except (TypeError, IndexError) as e:
                trial_vals[0] = analyzer.loops.conds[count].val

        for count3 in range(0, len(analyzer.loops.conds[count].repeats)):
            aux_trial = analyzer.loops.conds[count].repeats[count3].trialno
            trial_num[aux_trial - 1, :] = trial_vals

    if b_flag == 1:
        for blank_trial in range(0, len(analyzer.loops.conds[-1].repeats)):
            aux_trial = analyzer.loops.conds[-1].repeats[blank_trial].trialno
            trial_num[aux_trial - 1, :] = np.ones(trial_num.shape[1]) * float('nan')

    stim_time = np.zeros(3)
    for count4 in range(0, 3):
        stim_time[count4] = analyzer.P.param[count4][2]

    return trial_num, stim_time


# def analyzer_params(analyzer_path):
#     analyzer = load_analyzer(analyzer_path)
#     params = {}
#     for param in analyzer.P.param:
#         params[param[0]] = param[2]
#
#     return params


# def analyzer_pg_conds(analyzer_path):
#     trial_num, stim_time = analyzer_pg(analyzer_path)
#
#     analyzer_complete = sio.loadmat(analyzer_path, squeeze_me=True, struct_as_record=False)
#     analyzer = analyzer_complete['Analyzer']
#
#     columns = []
#     if type(analyzer.L.param[0]) != str:
#         for i in analyzer.L.param:
#                 columns.append(i[0])
#     else:
#         columns.append(analyzer.L.param[0])
#
#     trial_info = pd.DataFrame(trial_num, columns=columns)
#     return trial_info, stim_time


# def analyzer_pg(analyzer_path):
#     analyzer = load_analyzer(analyzer_path)
#
#     b_flag = 0
#     if analyzer.loops.conds[len(analyzer.loops.conds) - 1].symbol[0] == 'blank':
#         b_flag = 1
#
#     if b_flag == 0:
#         trial_num = np.zeros((len(analyzer.loops.conds) * len(analyzer.loops.conds[0].repeats),
#                              (len(analyzer.loops.conds[0].symbol))))
#     else:
#         trial_num = np.zeros(((len(analyzer.loops.conds) - b_flag) * len(analyzer.loops.conds[0].repeats) +
#                               len(analyzer.loops.conds[-1].repeats),
#                               (len(analyzer.loops.conds[0].symbol))))
#
#     for count in range(0, len(analyzer.loops.conds) - b_flag):
#         trial_vals = np.zeros(len(analyzer.loops.conds[count].symbol))
#
#         for count2 in range(0, len(analyzer.loops.conds[count].symbol)):
#             trial_vals[count2] = analyzer.loops.conds[count].val[count2]
#
#         for count3 in range(0, len(analyzer.loops.conds[count].repeats)):
#             aux_trial = analyzer.loops.conds[count].repeats[count3].trialno
#             trial_num[aux_trial - 1, :] = trial_vals
#
#     for blank_trial in range(0, len(analyzer.loops.conds[-1].repeats)):
#         aux_trial = analyzer.loops.conds[-1].repeats[blank_trial].trialno
#         trial_num[aux_trial - 1, :] = np.array([256, 256])
#
#     stim_time = np.zeros(3)
#     for count4 in range(0, 3):
#         stim_time[count4] = analyzer.P.param[count4][2]
#
#     return trial_num, stim_time


def analyzer_params(analyzer_path):
    analyzer = load_analyzer(analyzer_path)
    params = {}
    for param in analyzer.P.param:
        params[param[0]] = param[2]

    return params


def analyzer_pg_conds(analyzer_path):
    trial_num, stim_time = analyzer_pg(analyzer_path)

    analyzer_complete = sio.loadmat(analyzer_path, squeeze_me=True, struct_as_record=False)
    analyzer = analyzer_complete['Analyzer']

    columns = []
    for i in analyzer.L.param:
        columns.append(i[0])

    trial_info = pd.DataFrame(trial_num, columns=columns)
    return trial_info, stim_time

def anova_list(cell_F_stim, sort_param):
    # Function to calculate ANOVA from list of values
    list_F_stim = [cell_F_stim[cell_F_stim[sort_param] == p_param]['Mean'] for p_param in
                   np.unique(cell_F_stim[sort_param].values)]
    F_stat, p = sp.stats.f_oneway(*list_F_stim)
    return p

def gaus(x, a, x0, sigma):
    from scipy import asarray as ar, exp
    return a*exp(-(x-x0)**2/(2*sigma**2))
    
def matlab_style_gauss2D(shape=(3, 3), sigma=0.5):
    """
    2D gaussian mask - should give the same result as MATLAB's fspecial('gaussian',[shape],[sigma])
    """
    m, n = [(ss - 1.) / 2. for ss in shape]
    y, x = np.ogrid[-m:m + 1, -n:n + 1]
    h = np.exp(-(x * x + y * y) / (2. * sigma * sigma))
    h[h < np.finfo(h.dtype).eps * h.max()] = 0
    sumh = h.sum()
    if sumh != 0:
        h /= sumh
    return h

def apply_matlab_style_gauss2D(arr, filt):
    arr_filt = sp.signal.convolve2d(arr, np.rot90(filt), mode='same')
    return arr_filt

# def build_hartley(analyzer_path):
#     analyzer_complete = sio.loadmat(analyzer_path, squeeze_me=True, struct_as_record=False)
#     analyzer = analyzer_complete['Analyzer']
#
#     params = analyzer_params(analyzer_path)
def build_hartley(x_size, y_size, min_sf, max_sf):
    x_size = params['x_size']
    y_size = params['y_size']
    min_sf = params['min_sf']
    max_sf = params['max_sf']

    screen_xcm = analyzer.M.screenXcm
    screen_ycm = analyzer.M.screenYcm

    x_pixels = analyzer.M.xpixels
    y_pixels = analyzer.M.ypixels

    screen_dist = analyzer.M.screenDist

    pix_per_xcm = x_pixels / screen_xcm
    pix_per_ycm = y_pixels / screen_ycm

    x_cm = 2 * screen_dist * np.tan(x_size / 2 * np.pi / 180) + 0.1
    xN = int(np.round(x_cm * pix_per_xcm))

    y_cm = 2 * screen_dist * np.tan(y_size / 2 * np.pi / 180) + 0.1
    yN = int(np.round(y_cm * pix_per_ycm))

    xdom = np.linspace(-x_size / 2, x_size / 2, xN)
    ydom = np.linspace(-y_size / 2, y_size / 2, yN)
    xdom, ydom = np.meshgrid(xdom, ydom)
    r = np.sqrt(np.square(xdom) + np.square(ydom))

    oridom = np.array([0, 90, 180, 270])
    bwdom = np.array([-1, 1])

    xN = xN - np.fmod(xN, 2)
    yN = yN - np.fmod(yN, 2)

    nx = np.arange(xN)
    ny = np.arange(yN)

    dk = 1
    kx = np.arange(-xN / 2, (xN / 2) + 0.0001, 1)
    ky = np.arange(-yN / 2, (yN / 2) + 0.0001, 1)

    kx = kx[kx >= 0]
    ky = ky[ky >= 0]

    kxmat, kymat = np.meshgrid(kx, ky)
    nxmat, nymat = np.meshgrid(nx, ny)

    sfxdom = kxmat / x_size
    sfydom = kymat / y_size

    rsf = np.sqrt(np.square(sfxdom) + np.square(sfydom))
    kmatID = np.where((rsf <= max_sf) & (rsf >= min_sf))
    kxdom = np.zeros(np.shape(kmatID)[1])
    kydom = np.zeros(np.shape(kmatID)[1])

    for i, kID in enumerate(kmatID[0]):
        kxdom[i] = kxmat[kmatID[1][i], kmatID[0][i]]
        kydom[i] = kymat[kmatID[1][i], kmatID[0][i]]

    N_bw = np.size(bwdom)
    N_ori = np.size(oridom)
    N_kdom = np.size(kxdom)
    N_color = 1
    N_kxaxis = kxdom[kxdom == 0].size
    n_blanks = 4 * N_kxaxis * N_color

    N_comb = np.round(N_bw * N_ori * N_kdom * N_color)
    N_cond = N_comb + n_blanks

    cond_columns = ['ori', 'kx', 'ky', 'bw']
    cond = pd.DataFrame(columns=cond_columns)
    images = np.zeros([xN, yN, N_cond])
    condnum = 0

    for ori in oridom:
        if ori == 0:
            coord = np.array([1, 1])
        elif ori == 90:
            coord = np.array([-1, 1])
        elif ori == 180:
            coord = np.array([-1, -1])
        else:
            coord = np.array([1, -1])

        for i, kx in enumerate(kxdom):
            if (ori == 90) & (kydom[i] == 0):
                continue

            if (ori == 180) & (kx == 0):
                continue

            if (ori == 270) & (kx == 0 or kydom[i] == 0):
                continue

            k_x = kx * coord[0]
            k_y = kydom[i] * coord[1]

            for bw in bwdom:
                cond.loc[condnum] = [ori, k_x, k_y, bw]
                alpha = (2 * np.pi * k_x * nxmat / xN) + (2 * np.pi * k_y * nymat / yN)
                images[:, :, condnum] = (np.sin(alpha) + np.cos(alpha)) * bw
                condnum += 1

    blanks = pd.DataFrame(np.zeros([n_blanks, 4]), columns=cond_columns)
    cond_blanks = cond.append(blanks, ignore_index=True)

    return cond_blanks, images


def hartley_responsive(hartley_dict, expt, seg_file, cutoff=1.5):
    patch_list = []
    # hartley_dict = {}
    hartley = hartley_dict[expt]
    n_s_cell = hartley.shape[0]
    hartley_var = hartley.var(axis=(2, 3))   # Variance of an image/STA
    # Use this code to measure the location of the max variance
    hartley_var_all = hartley_var[np.arange(n_s_cell), np.argmax(hartley_var, axis=1)] / hartley_var[:, 17:20].mean(axis=1)   # Normolized by mean STAs of 17-20 (tau)

    hartley_cells = np.where(hartley_var_all > cutoff)[0]   # Select the cells that have STA variance bigger than threshold.

    max_h = []
    max_tau = np.argmax(hartley_var, axis=1)[hartley_cells]  # argmax Returns the indices of the maximum STA that has maximal variance for each hartley cell.
    # h_responsive = np.zeros((np.size(max_tau), 44, 44))
    # for i in range(0, np.size(max_tau)):
    #     if max_tau[i] <3 or max_tau[i] >6:
    #        max_tau[i] = 4
           ### Check here PL
        # h_responsive = np.mean(hartley[hartley_cells, max_tau-1:max_tau+2, :, :], axis=0)    # I am trying to reduce noise by averaging tau+-1
    h_responsive = hartley[hartley_cells, max_tau, :, :]
    for cell in np.arange(h_responsive.shape[0]):
        max_h.append(max(h_responsive.min(axis=(1, 2))[cell], h_responsive.max(axis=(1, 2))[cell], key=abs))   # in the maxiaml STA, compare abs(min, max), choose the bigger one as a cell's ON/OFF type.

    for i, h_cell in enumerate(hartley_cells):
        segment = sio.loadmat(seg_file, squeeze_me=True, struct_as_record=False)['vert'][h_cell]
        poly = Polygon([tuple(l) for l in list(segment)])
        if max_h[i] <= 0:    # OFF cell, minmal response's abs is bigger than maximal response's abs
            patch_list.append(PolygonPatch(poly, color='b'))
        else:                # ON cell
            patch_list.append(PolygonPatch(poly, color='r'))
    return patch_list, hartley_var_all, max_h


def hartley_responsive_LMS(hartley_dict, expt, LMS_hartley_list):

    hartley = hartley_dict[expt]
    # n_s_cell = hartley.shape[0]
    hartley_var = hartley.var(axis=(2, 3))   # Variance of an image/STA

    LMS_hartley_resp = []
    max_tau = np.argmax(hartley_var, axis=1)[LMS_hartley_list]    # argmax Returns the indices of STA that has maximal variance for each hartley cell.
    
    for i in range(0, np.size(max_tau)):
        if max_tau[i] <3 or max_tau[i] >6:
           max_tau[i] = 4
        h_responsive = np.mean(hartley[LMS_hartley_list, max_tau[i]-1:max_tau[i]+2, :, :], axis=1)
     
    for cell in np.arange(h_responsive.shape[0]):
        LMS_hartley_resp.append(max(h_responsive.min(axis=(1, 2))[cell], h_responsive.max(axis=(1, 2))[cell], key=abs))   # in the maxiaml STA, compare abs(min, max), choose the bigger one as a cell's ON/OFF type.
    return LMS_hartley_resp, max_tau


def get_plot_lim(min, max):
    lim = np.max(np.abs(np.array([min, max])))
    return lim