import numpy as np
import scipy.io as sio
import pandas as pd
import matplotlib.pyplot as plt
from shapely.geometry.polygon import Polygon
from descartes import PolygonPatch
from matplotlib.colors import ListedColormap, LinearSegmentedColormap
import pickle
import os
import glob
import warnings
import hdf5storage
import h5py
warnings.simplefilter('error', RuntimeWarning)

# Add details about imaging experiment (unit, expt, plane)
disk = 'O:'
animal = 'AF4'   # AF4, AF3
unit =   '004' #'004'  # '003'
# expt_hartley = ['001','002','003''004']
# expt_hartley_name = ['L-cone','M-cone','S-cone','Achromatic']
expt_hartley = '002'
expt_hartley_name = 'M'
plane = '001'  #'001'   #'000'

# Check which cell is selective
Thres = 0.30
staThres = 0.25  # for selecting file to load
# huecpiThres = -1  # -1: plot all, 0: more prefer hue, 0.33: strongly tuning to hue
#
numberofColor = 12
hueList = [0, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330]
colorSpace = 'DKL'

white_BK = True    # True for white/gray background, False for aligned 2P image background
BK_alpha = 0.5     # background alpha 
font_size = 15
ylimt_hist = 60

main_path = os.path.join(disk, animal, '2P_analysis')
featureData_path = os.path.join(main_path, 'Summary', 'DataExport')  # has ROIs,Bkgs, and data organized in feature dimenstion
unit_path = os.path.join(main_path, 'U%s' % unit)
planeData_path = os.path.join(unit_path,'_Summary', 'DataExport') # has data organized in plane dimension

expt_path_hartley = os.path.join(unit_path, '%s_%s_%s' % (unit, expt_hartley, plane), 'DataExport') #
expt_path_plot_hartley = os.path.join(unit_path, '%s_%s_%s' % (unit, expt_hartley, plane),'Plots') #
result_path_plane0 = os.path.join(unit_path,'_Summary', 'plane_%s'%plane)

result_path_plane = os.path.join(result_path_plane0, '0. Original maps')
result_path_hartley = os.path.join(unit_path, '%s_%s_%s' % (unit, expt_hartley, plane), 'Plots') #

file_name_ori = os.path.join('%s_%s_%s_%s_%s_ori' % (animal, unit, expt_hartley, plane,expt_hartley_name))
file_name_sf = os.path.join('%s_%s_%s_%s_%s_sf' % (animal, unit, expt_hartley, plane,expt_hartley_name))

roi_name = os.path.join('%s_u%s_plane%s' % (animal, unit, plane))

# Make folders
if not os.path.exists(result_path_plane0):
    os.mkdir(result_path_plane0)

if not os.path.exists(result_path_plane):
    os.mkdir(result_path_plane)

if (not os.path.exists(result_path_hartley)):
    os.mkdir(result_path_hartley)
resultCell_path_hartley = os.path.join(result_path_hartley, 'cells')
if (not os.path.exists(resultCell_path_hartley)):
    os.mkdir(resultCell_path_hartley)

## Define the color map for ploting
colormappath = 'C:\\Users\\<USER>\\.julia\\dev\\NeuroAnalysis\\src\\Visualization\\colormaps'
ori_lut = ListedColormap(sio.loadmat(os.path.join(colormappath, 'ori_lut_alpha0.mat'), squeeze_me=True, struct_as_record=False)['lut'])
# cmap_patch = plt.cm.get_cmap('hsv')
cmap_patch_sf = plt.cm.get_cmap('jet')
cmap_patch_cpi = plt.cm.get_cmap('jet')
cmap_patch_osi = plt.cm.get_cmap('jet')
cmap_patch_uniquehue = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'uniquehue.mat'), squeeze_me=True, struct_as_record=False)['lut'])
cmap_patch_hsl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_hsl_mshue_l0.4.mat'), squeeze_me=True, struct_as_record=False)['colors'])
cmap_patch_dkl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_dkl_mcchue_l0.mat'), squeeze_me=True, struct_as_record=False)['colors'])
cmap_patch_lidkl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_lidkl_mcchue_l0.mat'), squeeze_me=True, struct_as_record=False)['colors'])
if colorSpace  == 'DKL':
    color_hex_keys = ListedColormap(['#FF8080', '#FF80BF', '#FF80FF', '#BF80FF', '#8080FF', '#80BFFF', '#80FFFF',
             '#80FFBF', '#80FF80', '#BFFF80', '#FFFF80', '#FFBF80', '#808080'])   # DKL hues
elif colorSpace == 'HSL':
    color_hex_keys = ListedColormap(['#af1600', '#8a4600', '#5a5d01', '#2a6600', '#006a00', '#006931', '#006464',
             '#0058b6', '#002DFF', '#6a2ade', '#97209b', '#aa1c50', '#808080'])   # HSL hues
gray = '#B4B4B4'    # '#808080'


## load background images and ROIs
# I use images from orisf_achromatic data as background image. All maps are plotted on this image.
roibkg_file = os.path.join(planeData_path,'%s_%s_%s_roibkg.jld2' %(animal, unit, plane))
roibkg = h5py.File(roibkg_file, 'r+')
bkgimg = roibkg['bkg'][()]
whitBK = np.ones(np.transpose(bkgimg).shape)*0.8

# load roi segements/contours
rois = roibkg["roi"][()]
# obj = roibkg[objref[0]]
# rois = obj.value
# Transform roi contours to patches
allROIs = []   # patches of all ROIs
for i in range(np.size(rois)):
    allROIs.append(0)    # append 0 if there is ROI
    #Find the segment/contour for this cell
    roibkg[rois[i]][()] = roibkg[rois[i]][()].astype(int) 
    allROIs[i] = Polygon([tuple(l) for l in list(np.transpose(roibkg[rois[i]][()]-1))])

## Load Result files
# data_file = os.path.join(planeData_path,'%s_%s_thres%s_fourier_dataset_organized.csv' %(animal, unit, staThres))
data_file = os.path.join(expt_path_plot_hartley,'%s_%s_%s_%s_%s_tuning_result.csv' %(animal, unit, expt_hartley, plane, expt_hartley_name))
plda = pd.read_csv(data_file, index_col=0)
# exp_params_data = hdf5storage.loadmat(exp_params)
# plane_data = h5py.File(data_file, 'r+')
# objref = plane_data[unit+'_'+plane][()]
# obj = [plane_data[ref] for ref in objref]
# # obj = plane_data[objref[0]]
# plda = obj.value

# plane_data_sum = h5py.File(data_file, 'r+')[unit+'_'+plane+'_'+'sum']
# objref = plane_data_sum[()]
# obj = plane_data_sum[objref[0]]
# plda_sum = obj.value

# visual responsive
kdelta = plda.kdelta
numCell = np.size(kdelta)
sig = kdelta > Thres
# visResp =  plda.visResp
# numCell = np.size(visResp)

# orientation/direction/sf selective from achromatic grating
ori_est = plda.orimean
sf_est = plda.sfmean
sf_max = max(sig*sf_est)

## Plot orientation, direction and sf maps from achromatic stimuli
fig, ax = plt.subplots()
if white_BK:
    ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
else:
    ax.imshow(bkgimg, cmap='gray')

for vi in np.arange(numCell):
    if ~sig[vi]:
        ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
    if sig[vi]:
        ax.add_patch(PolygonPatch(allROIs[vi], alpha=1 , color=ori_lut(ori_est[vi]/180)))  #alpha=np.log10(ori_p[vi])/np.min(np.log10(ori_p)) alpha=1-(ori_p[vi]-np.min(ori_p[:]))/(np.max(ori_p[:])-np.min(ori_p[:])),
ax.set_rasterized(True)
plt.axis('off')
# plt.savefig('%s/%s_auc%s_cv.svg' % (result_path_ori,  file_name_ori), facecolor='#ffffff', dpi=300, format='svg')
# plt.savefig('%s/%s_thres%s.png' % (result_path_plane, file_name_ori, Thres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
plt.savefig('%s/%s_thres%s_mean.png' % (result_path_hartley, file_name_ori, Thres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')


# plot OSI map, osi is calculated after fitting
# fig, ax = plt.subplots()
# if white_BK:
#     ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
# else:
#     ax.imshow(bkgimg, cmap='gray')
# for vi in np.arange(numCell):
#     if ~sig[vi]:
#         ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
#     if sig[vi]:
#         if np.isnan(fit_osi[vi]):
#             ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=gray))
#         else:
#             ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=cmap_patch_osi(fit_osi[vi])))
# # ax.set_title( file_name_ori, fontsize=font_size)
# ax.set_rasterized(True)
# plt.axis('off')
# # plt.savefig('%s/%s_osi_fit.svg' % (result_path_ori,  file_name_ori), facecolor='#ffffff', dpi=300, format='svg')
# plt.savefig('%s/%s_osi_fit.png' % (result_path_plane, file_name_ori), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
# plt.savefig('%s/%s_osi_fit.png' % (result_path_ori, file_name_ori), dpi=300, bbox_inches='tight', pad_inches=0, format='png')


# Plot spatial frequency preference map using fitting parameters
fig, ax = plt.subplots()
if white_BK:
    ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
else:
    ax.imshow(bkgimg, cmap='gray')

for vi in np.arange(numCell):
    if ~sig[vi]:
        ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
    if sig[vi]:
        ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=cmap_patch_sf(sf_est[vi]/sf_max)))
ax.set_rasterized(True)
# ax.set_title('Spatial Frequency Tuning Map', fontsize=font_size)
plt.axis('off')
# plt.savefig('%s/%s_sf.svg' % (result_path_ori, file_name_sf), dpi=300, format='svg')
# plt.savefig('%s/%s_thres%s.png' % (result_path_plane, file_name_sf,Thres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
plt.savefig('%s/%s_thres%s_mean.png' % (result_path_hartley, file_name_sf,Thres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
