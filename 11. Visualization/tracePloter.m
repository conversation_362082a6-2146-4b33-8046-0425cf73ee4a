% 509,562 24234.51601829556,


clear
clc
dataMainPath = '/media/peichao/PL_NHP2_AE7AE8AF4/AF4/2P_data/';
resultMainPath = '/media/peichao/PL_NHP2_AE7AE8AF4/AF4/2P_analysis/';
aniName = 'AF4';
planeId = 'plane_001';
unitId = '004';
trialId = {'001','002','003','004'};
exptId = {'L','M','S','A'};
cellId = [427,399,621,485];  %503, %M525
% dataLeg = 1800;
% blkResp = [24526.891521872334, 22930.040996721502, 25407.254233517164, 34469.533902073985]; % 35362.086431143485,
% plotRg = [11000:11000+dataLeg-1;49500:49500+dataLeg-1;35600:35600+dataLeg-1;60000:60000+dataLeg-1];
dataLeg = 600;
blkResp = [24526.891521872334, 22622.27971801022, 25407.254233517164, 34469.533902073985]; % 35362.086431143485,
plotRg = [11600:11600+dataLeg-1;42000:42000+dataLeg-1;35600+600:35600+600+dataLeg-1;60000+600:60000+600+dataLeg-1];
colorId = {'#FE717F','#00AF7D','#A261FE','k'};
plotSingle = 1;

fileName = strcat(aniName, '_', unitId, '_', planeId, '_trace_');

cellNum_p0 = 782;
cellNum_p1 = 817;

%%
resultFolder = strcat(resultMainPath,'U', unitId, '/_Summary/', planeId, '/0. Original maps/');
if ~isfolder(resultFolder)
    mkdir(resultFolder);
end 


%%
Lwidth = 0.5;
% X Y axis
aRange = [0 600];
bRange = [-14.5 2];  % Range
axThickness = 1;
lnThickness = 2;
atickLabel = {'0','10','20', '30','40','50', '60'};
btickLabel = {};

% X Y ticks
tickFront =9;
x_ticks = 0:100:600;
y_ticks = -14.5:0.5:2;

% axis & line color
axColor = [0 0 0];
lnColor = [0 0 0];

f = figure;
% f.InnerPosition = [10 10 3000 300];  % Define drawable region

%%
result = struct();
result.trace = zeros(2*length(trialId), dataLeg);
result.train = zeros(2*length(trialId), dataLeg);
spike_curve=cell(2*length(trialId),1);
ct = 1;
ct2 = 0;
for ii = 1: length(trialId)
    
    ii
    
    fileName1 = strcat(aniName,'_U', unitId, '_', exptId(ii), '_', planeId, '_', num2str(cellId(ii)),'_trace');
    fileName2 = strcat(aniName,'_U', unitId, '_', exptId(ii), '_', planeId, '_', num2str(cellId(ii)),'_train');
    dataFolder = strcat(dataMainPath,'U', unitId,'/', unitId, '_', trialId{1,ii}, '/');

    cd(dataFolder)
    data = dir('*_merged.signals');

    load(data.name, '-mat')

    rawF = sig(:,cellNum_p0+cellId(ii));
    dF = (rawF-blkResp(ii))/blkResp(ii);

    spk = spks(:,cellNum_p0+cellId(ii));
    
    spike_curve{ct} = dF(plotRg(ii,:));
    ct =ct+1;
    spike_curve{ct} = spk(plotRg(ii,:));
    result(ii,:).trace = dF(plotRg(ii,:));
    result(ii,:).train = spk(plotRg(ii,:));
    ct =ct+1;
    % Plot
    if plotSingle == 1
        cellId(ii)
%         % plot(dF(49500:49500+1800))
% 
%         Lwidth = 0.5;
%         % X Y axis
%         aRange = [0 1800];
%         bRange = [-0.5 1.5];  % Range
%         axThickness = 1;
%         lnThickness = 2;
%         atickLabel = {'0','10','20', '30','40','50', '60'};
%         btickLabel = {};
% 
%         % X Y ticks
%         tickFront = 30;
%         x_ticks = 0:300:1800;
%         % y_ticks = 40:20:140;
% 
%         % axis & line color
%         axColor = [0 0 0];
%         lnColor = [0 0 0];
% 
%         f = figure;
%         f.InnerPosition = [10 10 3000 300];  % Define drawable region

        plot(dF(plotRg(ii,:))-ct2,'Color', colorId{1,ii}, 'LineWidth', Lwidth)
        
        hold on;
        ct2 =ct2+2;
%         xlim(aRange)
%         ylim(bRange)
%         xticks(x_ticks)
%         % yticks(y_ticks)
%         xticklabels(atickLabel)
%         % xticklabels({})
%         % yticklabels({})
%         set(gca,'FontSize',tickFront)
%         set(gca, 'linewidth',axThickness)
% 
%         grid off
%         box off

        % savefig([resultFolder, fileName, '.fig'])
%         export_fig (gcf, [resultFolder, fileName1, '.png'])
%         fig2svg([resultFolder, fileName1, '.svg'])
% 
%         %----------------------------------
%         f = figure;
%         f.InnerPosition = [10 10 3000 300];  % Define drawable region

        plot(spk(plotRg(ii,:))-ct2,'Color', colorId{1,ii}, 'LineWidth', Lwidth)
        
        hold on;
        ct2 =ct2+2;
% 
%         xlim(aRange)
%         ylim([-0.1 1.9])
%         xticks(x_ticks)
%         % yticks(y_ticks)
%         xticklabels(atickLabel)
%         % xticklabels({})
%         % yticklabels({})
%         set(gca,'FontSize',tickFront)
%         set(gca, 'linewidth',axThickness)
% 
%         grid off
%         box off
%         % savefig([resultFolder, fileName, '.fig'])
%         export_fig (gcf, [resultFolder, fileName2, '.png'])
%         fig2svg([resultFolder, fileName2, '.svg'])
    end
end

xlim(aRange)
ylim(bRange)
xticks(x_ticks)
yticks(y_ticks)
xticklabels(atickLabel)
% xticklabels({})
% yticklabels({})
set(gca,'FontSize',tickFront)
set(gca, 'linewidth',axThickness)

grid off
box off
% % savefig([resultFolder, fileName, '.fig'])
% export_fig (gcf, [resultFolder, fileName2{1,1}, '_new.png'])
fig2svg([resultFolder, fileName2{1,1}, '_new.svg'])

save([resultFolder, fileName, 'result.mat'], 'result', '-v7.3');