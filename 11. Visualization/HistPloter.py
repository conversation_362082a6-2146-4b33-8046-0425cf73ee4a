import numpy as np
import scipy.io as sio
import pandas as pd
import matplotlib.pyplot as plt
from shapely.geometry.polygon import Polygon
from descartes import PolygonPatch
from matplotlib.colors import ListedColormap, LinearSegmentedColormap
import pickle
import os
import glob
import warnings
import hdf5storage
import h5py
warnings.simplefilter('error', RuntimeWarning)

# Add details about imaging experiment (unit, expt, plane)
disks = ['K:','G:']
animals = ['AE6','AE7']   # AF4, AF3

# Check which cell is selective
oriaucThres = 0.7
diraucThres = 0.7
hueaucThres = 0.8

# Selectivity defined by circular variance
# oricvThres = 0.7
# dircvThres = 0.7
# huecvThres = 0.7

# Selectivity defined by selective index
# osiThres = 0.33
# dsiThres = 0.33
# huesiThres = 0.33
huecpiThres = -1  # -1: plot all, 0: more prefer hue, 0.33: strongly tuning to hue

numberofColor = 12
hueList = [0, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330]
colorSpace = 'HSL'

individual = False
multiple = True

white_BK = True    # True for white/gray background, False for aligned 2P image background
BK_alpha = 0.5     # background alpha 
font_size = 20
ylimt_hist = 800
ylimt_hist_all = 800
## Define the color map for ploting
colormappath = 'C:\\Users\\<USER>\\.julia\\dev\\NeuroAnalysis\\src\\Visualization\\colors'
ori_lut = ListedColormap(sio.loadmat(os.path.join(colormappath, 'ori_lut_alpha0.mat'), squeeze_me=True, struct_as_record=False)['lut'])
# cmap_patch = plt.cm.get_cmap('hsv')
cmap_patch_sf = plt.cm.get_cmap('jet')
cmap_patch_cpi = plt.cm.get_cmap('jet')
cmap_patch_osi = plt.cm.get_cmap('jet')
cmap_patch_uniquehue = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'uniquehue.mat'), squeeze_me=True, struct_as_record=False)['lut'])
cmap_patch_hsl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_hsl_mshue_l0.4.mat'), squeeze_me=True, struct_as_record=False)['colors'])
cmap_patch_dkl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_dkl_mcchue_l0.mat'), squeeze_me=True, struct_as_record=False)['colors'])
cmap_patch_lidkl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_lidkl_mcchue_l0.mat'), squeeze_me=True, struct_as_record=False)['colors'])
if colorSpace  == 'DKL':
    color_hex_keys = ListedColormap(['#FF8080', '#FF80BF', '#FF80FF', '#BF80FF', '#8080FF', '#80BFFF', '#80FFFF',
             '#80FFBF', '#80FF80', '#BFFF80', '#FFFF80', '#FFBF80', '#808080'])   # DKL hues
elif colorSpace == 'HSL':
    color_hex_keys = ListedColormap(['#af1600', '#8a4600', '#5a5d01', '#2a6600', '#006a00', '#006931', '#006464',
             '#0058b6', '#002DFF', '#6a2ade', '#97209b', '#aa1c50', '#808080'])   # HSL hues
gray = '#B4B4B4'    # '#808080'

## Initialize plotting
print('Processing.......')
Hue_cellNum = 0
Hue_hist = pd.Series([])

for j, ani in enumerate(animals):
    animal = animals[j]
    disk = disks[j]
    
    main_path = os.path.join(disk, animal, '2P_analysis')
    featureData_path = os.path.join(main_path, 'Summary', 'DataExport')  # has ROIs,Bkgs, and data organized in feature dimenstion
    result_path_allunit0 = os.path.join(main_path, 'Summary', 'Plots')
    result_path_allunit = os.path.join(result_path_allunit0,'Hue')

    # Make folders for saving data from all units
    if not os.path.exists(result_path_allunit0):
        os.mkdir(result_path_allunit0)
    if not os.path.exists(result_path_allunit):
        os.mkdir(result_path_allunit)

    ## Load Result files
    huedata_file = glob.glob(featureData_path +'/*hueData.csv')[0]
    # huedata_file = os.path.join(featureData_path,'%s_*_hueData.csv' %(animal))
    huedata = pd.read_csv(huedata_file, index_col=0)

    ## Read data

    # hue selective
    hue_ax_auc = huedata.hueaxauc
    hue_di_auc = huedata.huediauc

    hue_ax_cv = huedata.hueaxcv
    hue_di_cv = huedata.huedicv

    cv_hue_ax = huedata.cvhueax  # from vector summation
    cv_hue_di = huedata.cvhuedi
    fit_hue_ax = huedata.fithueax  # from fitting
    fit_hue_di = huedata.fithuedi
    fit_hue_ax_si = huedata.hueaxsi # selective index
    fit_hue_di_si = huedata.huedisi
    max_hue = huedata.maxhue  # no vector summation or fitting
    max_hue_resp = huedata.maxhueresp

    # orientation/direction/sf selective from Hue grating
    hueori_auc = huedata.hueoriauc
    huedir_auc = huedata.huedirauc

    hueori_cv = huedata.hueoricv
    huedir_cv = huedata.huedircv

    cv_hueori = huedata.cvhueori  # from vector summation
    cv_huedir = huedata.cvhuedir
    fit_hueori = huedata.fithueori  # from fitting
    fit_huedir = huedata.fithuedir
    fit_hueosi = huedata.hueosi
    fit_huedsi = huedata.huedsi

    fit_huesf = huedata.fithuesf

    # # orientation/direction/sf selective from achromatic grating
    oridata_file = glob.glob(featureData_path +'/*oriData.csv')[0]
    oridata = pd.read_csv(oridata_file, index_col=0)

    ori_auc = oridata.oriauc
    dir_auc = oridata.dirauc

    ori_cv = oridata.oricv  # cv of orientation  (1-mag)
    dir_cv = oridata.dircv  # cv of direction (1-mag)

    cv_ori = oridata.cvori  # preferred orientation from vector summation/cv
    cv_dir = oridata.cvdir # preferred direction from vector summation/cv
    fit_ori = oridata.fitori  # preferred orientation from cv
    fit_dir = oridata.fitdir
    fit_osi = oridata.osi
    fit_dsi = oridata.dsi

    fit_sf = oridata.fitsf

    ## histogram of hue selective cells.
    # visual responsive
    visResp =  huedata.visResp | oridata.visResp
    numCell = np.size(visResp)
    ## Calculate CPI
    hueResp = max_hue_resp
    hueResp = 1-np.min(pd.concat([hue_ax_cv, hue_di_cv], axis=1),axis=1)
    achroResp = 1-ori_cv
    cpi = (hueResp - achroResp) / (hueResp + achroResp)

    # if colorSpace == 'DKL':
    hue_cellNum = np.sum(((hue_ax_auc > hueaucThres) | (hue_di_auc > hueaucThres)) & (visResp > 0))# & (cpi>huecpiThres)) 
    hue_hist = max_hue[((hue_ax_auc > hueaucThres) | (hue_di_auc > hueaucThres)) & (visResp > 0)]# & (cpi>huecpiThres)]
    # elif colorSpace == 'HSL':
    #     hue_cellNum = np.sum((hue_di_auc > hueaucThres) & (visResp > 0))# & (cpi>huecpiThres))
    #     hue_hist = max_hue[(hue_di_auc > hueaucThres) & (visResp > 0)]# & (cpi>huecpiThres)]

    print(hue_cellNum)
    Hue_cellNum += hue_cellNum
    Hue_hist = pd.concat([Hue_hist,hue_hist],ignore_index=True)
 ## Plot individual animal
    if individual:
   
        fig, ax = plt.subplots()

        if numberofColor == 12:
            Hist = hue_hist
        elif numberofColor == 13:   # including visual responsive cells as gray color
            visHist = visResp.copy()
            for i in np.arange(numCell):
                if visResp[i] in hue_hist.index.values:
                    visHist.remove(visResp[i])
                    visual_values =  np.ones(np.size(visHist))*27  # use 27 represent visual responsive cells
                    visual_hist =  pd.Series(visual_values, index=visHist)
            Hist = hue_hist.append(visual_hist)

        histPatch = Hist.plot(kind='hist', bins = 12, rwidth=0.8)
        # Set colors on hist patches
        for patches in range(0, 12):
            if colorSpace == 'DKL':
                histPatch.patches[patches].set_color(cmap_patch_lidkl(patches/12))
            elif colorSpace == 'HSL':
                histPatch.patches[patches].set_color(color_hex_keys(patches))
            # histPatch.patches[patches].set_color(cmap_patch_hue1(patches/36))


        # ax.axes.get_xaxis().set_visible(False)
        plt.tick_params(
            axis='x',  # changes apply to the x-axis
            labelsize='large',
            which='both',  # both major and minor ticks are affected
            bottom=False,  # ticks along the bottom edge are off
            top=False,  # ticks along the top edge are off
            labelbottom=False)  # labels along the bottom edge are off
        plt.ylabel('Cell Number', fontsize = font_size)
        plt.xlabel('Preferred Hue', fontsize = font_size)
        plt.ylim(0, ylimt_hist)
        # plt.xlim(0, 330)
        # ax.set(xticks=range(0, 340, 30))
        # plt.grid(b=True, which='major', axis='y', alpha = 0.5, linewidth=1)
        ax.set_axisbelow(True)

        data_name = str.split(huedata_file,'_')[2]
        file_name_hue = os.path.join('%s_%s_allplane_hue' % (animal, data_name))
        plt.savefig('%s/%s_cellNum%s_aucThres%s_cpiThres%s_hist.svg' % (result_path_allunit, file_name_hue, str(hue_cellNum), hueaucThres, huecpiThres), dpi=300, format='svg')
        plt.savefig('%s/%s_cellNum%s_aucThres%s_cpiThres%s_hist.png' % (result_path_allunit, file_name_hue, str(hue_cellNum), hueaucThres, huecpiThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
        plt.show()


if multiple:
    print(Hue_cellNum)
    # Make folders for saving data from all cases
    result_path_allcase = os.path.join(result_path_allunit,'Allcase')
    if not os.path.exists(result_path_allcase):
        os.mkdir(result_path_allcase)

## Plot
    fig, ax = plt.subplots()
    if numberofColor == 12:
        Hist = Hue_hist
    elif numberofColor == 13:   # including visual responsive cells as gray color
        visHist = visResp.copy()
        for i in np.arange(numCell):
            if visResp[i] in hue_hist.index.values:
                visHist.remove(visResp[i])
                visual_values =  np.ones(np.size(visHist))*27  # use 27 represent visual responsive cells
                visual_hist =  pd.Series(visual_values, index=visHist)
        Hist = hue_hist.append(visual_hist)

    histPatch = Hist.plot(kind='hist', bins = 12, rwidth=0.8)
    # Set colors on hist patches
    for patches in range(0, 12):
        if colorSpace == 'DKL':
            histPatch.patches[patches].set_color(cmap_patch_lidkl(patches/12))
        elif colorSpace == 'HSL':
            histPatch.patches[patches].set_color(color_hex_keys(patches))
        # histPatch.patches[patches].set_color(cmap_patch_hue1(patches/36))


    # ax.axes.get_xaxis().set_visible(False)
    plt.tick_params(
        axis='x',  # changes apply to the x-axis
        labelsize='large',
        which='both',  # both major and minor ticks are affected
        bottom=False,  # ticks along the bottom edge are off
        top=False,  # ticks along the top edge are off
        labelbottom=False)  # labels along the bottom edge are off
    plt.ylabel('Cell Number', fontsize = font_size)
    plt.xlabel('Preferred Hue', fontsize = font_size)
    plt.ylim(0, ylimt_hist_all)
    # plt.xlim(0, 330)
    # plt.grid(b=True, which='major', axis='y', alpha = 0.5, linewidth=1)
    ax.set_axisbelow(True)

    data_name = str.split(huedata_file,'_')[2]
    file_name_hue = os.path.join('%s_%s_allplane_hue' % ('_'.join(animals), data_name))
    plt.savefig('%s/%s_cellNum%s_aucThres%s_cpiThres%s_hist.svg' % (result_path_allcase, file_name_hue, str(Hue_cellNum), hueaucThres, huecpiThres), dpi=300, format='svg')
    plt.savefig('%s/%s_cellNum%s_aucThres%s_cpiThres%s_hist.png' % (result_path_allcase, file_name_hue, str(Hue_cellNum), hueaucThres, huecpiThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
    plt.show()