
% Peichao: Plot single color keys as ring

%% Inputs
clc
clear
colorType = 'L'; % 'DKLmax', 'DKLmatch', 'HSL', 'Unique', 'L', 'M', 'S'
colorNum = 12;
keySize = 100; % pixel
keyShape = 'square'; % 'square', 'circle'
plotLine = 1;  % 1: plot lines with multiple colors; 0: plot shape, circle or square.
makeMovie = 1;
cycNum = 60;   % also means 60 sec for the video.
%% load color table
cmapFolder = 'C:\Users\<USER>\Dropbox\Github\2P\12. Visualization\';
cd(cmapFolder)

if strcmp(colorType,'DKLmax')
    load('cm_lidkl_mcchue_l0.mat')
elseif strcmp(colorType, 'DKLmatch')
    load('cm_dkl_mcchue_l0.mat')
elseif strcmp(colorType, 'HSL')
    load('cm_hsl_mshue_l0.4.mat')
elseif strcmp(colorType, 'Unique')
    load('uniquehue.mat')
elseif strcmp(colorType, 'Ori')
    load('ori_lut_alpha0.mat')
elseif strcmp(colorType, 'Dir')
    load('ori_lut_alpha0.mat')
elseif strcmp(colorType, 'L')
    load('ConeIsolating_L_lut.mat')
elseif strcmp(colorType, 'M')
    load('ConeIsolating_M_lut.mat')
elseif strcmp(colorType, 'S')
    load('ConeIsolating_S_lut.mat')
end

% huelut=flip(hsv(256),1);
% lut=[huelut(:,1:3); [1,1,1]];

lut=colors(:,1:3);
lut(361,:) = [1 1 1];

% lut=lut(:,1:3);
% lut(257,:) = [1 1 1];

colorStep = 360/colorNum;
colorAng = [0:colorStep:360];

resultFolder = strcat(cmapFolder, 'ColorMap\');
if ~isfolder(resultFolder)
    mkdir(resultFolder);    
end

%% Plot images
if plotLine ==0
    halfsize = round(keySize/2);
    aa=-halfsize:halfsize;
    [xx,yy] = meshgrid(aa,aa);
    zz = xx+1i*yy;
    % zz = abs(zz).*exp(1i*angle(zz)*2);
    za=flip(round(rad2deg(angle(zz)))+180,2);
    za(za==0)=360;
    % za=round(za./360.*256);
    % za=mod(za,180)+1;
    % za=round(za./180.*256);

    centerX = 0;
    centerY = 0;
    radius1 = halfsize-2;
    radius2 = 0;
    if strcmp(keyShape,'circle') % 'circle'
        Pixels1 = (yy - centerY).^2 + (xx - centerX).^2 <= radius1.^2;
        Pixels2 = (yy - centerY).^2 + (xx - centerX).^2 >= radius2.^2;
        Pixels = Pixels1.*Pixels2;
    elseif strcmp(keyShape, 'square') % Square
        Pixels = (abs(xx) < radius1) .* (abs(yy) < radius1);
    end
    img=Pixels.*za;

    for ii = 1:colorNum
        img1=img;
        img1(img1~=0)=colorAng(ii+1);
        img1(img==0)=361;
        img1(1,1)=1;
        imgName = strcat(colorType, num2str(colorAng(ii+1)), '_ColorKey_', keyShape);
        figure
        colormap(lut)
        imagesc(img1)
        ax = gca;
        set(ax,'xtick',[])
        set(ax,'xticklabel',[])
        set(ax,'ytick',[])
        set(ax,'yticklabel',[])
        axis off
        axis square

        saveas(gcf, [resultFolder, imgName, '.tif'])
    end
    close all
    
elseif plotLine == 1

    if makeMovie == 1
        vidfile = VideoWriter([resultFolder, 'testmovie.mp4'],'MPEG-4');
        open(vidfile);
        frameNum = 24*cycNum;
        xx = single(linspace(0,cycNum*pi,frameNum)); 
        yy = sin(xx);
        sz=10;  % size of dots
        f = figure('visible','off');
        f.InnerPosition = [10 10 1000 100];  %
        plot(xx(1),yy(1), 'k.','MarkerSize',sz);
        xlim([0 pi*cycNum+1])
        ylim([-1.1 1.1])
        ax = gca;

        set(ax,'xtick',[])
        set(ax,'xticklabel',[])
        set(ax,'ytick',[])
        set(ax,'yticklabel',[])
        axis off
        axis tight
        set(gca,'nextplot','replacechildren');
        set(gcf,'Renderer','zbuffer');
        F = getframe;
        height = size(F.cdata,1);
        width = size(F.cdata,2);
%         writeVideo(vidfile,F.cdata);
        
        for ii = 1:frameNum

            hold on
%             plot(xx(ii), yy(ii), 'k.','MarkerSize',sz)
            plot(xx(ii), 0, 'k.','MarkerSize',sz)
            xlim([0 pi*cycNum+1])
            ylim([-1.1 1.1])
%             pause(0.1)
            F = getframe;
            writeVideo(vidfile,F.cdata(1:height,1:width,:,:));
        end
        close(vidfile);
    else
        sz=100;  % size of dots
        f=figure; 
        f.InnerPosition = [10 10 600 200];  %
        lut2=lut(1:360,:);
        colormap(lut2)

        xx = single(linspace(0,2*pi,360)); 
        yy = sin(xx);
    %     yy = square(xx,50);
        c = linspace(1,360,length(xx));
        scatter(xx,yy, sz, c,'filled');

        hold on;
        xx = single(linspace(2*pi,4*pi,360)); 
        yy = sin(xx);
    %     yy = square(xx,50);
        c = linspace(1,360,length(xx));
        scatter(xx,yy, sz, c,'filled');

        ax = gca;
        set(ax,'xtick',[])
        set(ax,'xticklabel',[])
        set(ax,'ytick',[])
        set(ax,'yticklabel',[])
        axis off
        imgName = strcat(colorType, '_ColorKey_squareLine');
        saveas(gcf, [resultFolder, imgName, '.tif'])
        saveas(gcf, [resultFolder, imgName, '.svg'])
        close all

    end  
end


