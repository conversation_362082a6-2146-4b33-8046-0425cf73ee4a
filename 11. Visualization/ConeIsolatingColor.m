
% Peichao: I write this code for stimulus illustration 
% To create grating diagram with different color (cone-isolating, DKL, gray), SF, Ori.
% To calculate the look-up-table of cone-isolating and DKL-axis color
% To demostrate cone-isolating modulation

clc
clear
%% Inputs
plotModulation = 0;  % sine waves indicate the RBG guns modulation
st_modulation = 0; % 0: temporal modulation; 1: spatialtemporal modulation

colormod = 5;   % The color mode here is corresponding to different gun gains, see below.
colorgray = 1;

cycNum = 10; % how many cycles in the video

ori = 0;
sprofile = 'square'; % 'sine' or 'square'
tprofile = 'square'; % 'sine' or 'square'
s_freq = 0.8; 
s_duty = 0.5;
t_duty = 0.5;
t_period = 240;  % to make video 24f/cyc is 1cyc/sec because .mp4 is 24fps
st_phase = 270;
contrast = 100;
sizeDeg = 10;   % Stim size in deg
XsizePix = 361; % Stim size in pixel

aa = -180:180;
[xx,yy] = meshgrid(aa,aa);
zz = xx+1i*yy;
za=flip(round(rad2deg(angle(zz)))+180,2);
za(za==0)=360;
centerX = 0;
centerY = 0;
radius = 178;
mask = (yy - centerY).^2 + (xx - centerX).^2 <= radius.^2;


if plotModulation == 1
    YsizePix = 1;
else
    YsizePix = XsizePix;
end


    % for ii = 1:8
    %     
    %     colormod = ii;

switch colormod

    case 1  %this obeys the input gain values

        rgain = 1; 
        ggain = 1;
        bgain = 1;
        imgName = 'Gray';
    case 2  %L-cone isolation

        % AF5 for hartley (10 deg fundamentals)
        rgain = 1.000000;
        ggain = -0.108588;
        bgain = 0.002153; 
        imgName = 'ConeIsolating_L'; 

    case 3  %M-cone isolation
        rgain = -1.000000;
        ggain = 0.379002;
        bgain = -0.018731;
        imgName = 'ConeIsolating_M'; 

    case 4  %S-cone isolation

        rgain = 0.270728;
        ggain = -0.233443;
        bgain = 1.000000;
        imgName = 'ConeIsolating_S';

    case 5  %DKL0

        rgain = 255;
        ggain = 82;
        bgain = 129;
        imgName = 'DKL_0';

    case 6  %L+M

        rgain = 0.026101;
        ggain = 1.000000;
        bgain = -0.061421;
        imgName = 'DKL_W';

    case 7 %S - (L+M)

        rgain = 0.244024;
        ggain = -0.849438;
        bgain = 1.000000;
        imgName = 'DKL_Son';	

    case 8 %-S + (L+M)  % add this one, Peichao

        rgain = -0.244024;
        ggain = 0.849438;
        bgain = -1.000000;
        imgName = 'DKL_Soff';	
end


cmapFolder = 'C:\Users\<USER>\Dropbox\Github\2P\12. Visualization\';
cd(cmapFolder)
resultFolder = strcat(cmapFolder, 'ColorMap\Gratings\Temporal\');
if ~isfolder(resultFolder)
    mkdir(resultFolder);    
end
vidfile = VideoWriter([resultFolder, imgName,'.mp4'],'MPEG-4');
open(vidfile);
%% Create grating
x_ecc = round(sizeDeg/2);
y_ecc = round(sizeDeg/2);
x_ecc = single(linspace(-x_ecc,x_ecc,XsizePix));  %deg
y_ecc = single(linspace(-y_ecc,y_ecc,YsizePix));

[x_ecc y_ecc] = meshgrid(x_ecc,y_ecc);  %deg
sdom = x_ecc*cos(ori*pi/180) - y_ecc*sin(ori*pi/180);    %deg
sdom = sdom*s_freq*2*pi; %radians
tdom = single(linspace(0,2*pi,t_period+1));
tdom = tdom(1:end-1);
% Im = cos(sdom - tdom(1) - st_phase*pi/180);
for repeat = 1:cycNum
    
        for i = 1:length(tdom)
            if st_modulation
                Im = cos(sdom - tdom(i) - st_phase*pi/180);
                if strcmp(sprofile, 'sine')
                    Im = Im*contrast/100;  %[-1 1]
                elseif strcmp(sprofile, 'square')
                    thresh = cos(s_duty*pi);
                    Im = sign(Im-thresh);
                    Im = Im*contrast/100;
                end
            else
                amp = sin(tdom(i));
                if strcmp(tprofile, 'sine')
                    
                elseif strcmp(tprofile, 'square')
                    thresh = cos(t_duty*pi);
                    amp = sign(amp-thresh);
                end
                Im = ones(size(mask,1), size(mask,2)) .* amp;
            end
            Im = Im.*mask;
            % figure;
            % plot(Im(1,:))
        
            ImRGB = zeros(length(Im(:,1)),length(Im(1,:)),3,'uint8');  %make ImRGB uint8
            
            if colorgray ~= 1
                ImRdum0 = Im*rgain;   %[-1 1]
                ImGdum0 = Im*ggain;
                ImBdum0 = Im*bgain;

                ImRdum = (ImRdum0+1)/2;  %[0 1]
                ImGdum = (ImGdum0+1)/2;  
                ImBdum = (ImBdum0+1)/2;  

                ImRGB(:,:,1) = round(ImRdum*255);  %[0 255]
                ImRGB(:,:,2) = round(ImGdum*255);
                ImRGB(:,:,3) = round(ImBdum*255);
            elseif colorgray == 1
                blackcolor = [128 128 128];
                Imwhitecolorphase = Im;
                Imwhitecolorphase(Imwhitecolorphase<0) = 0;
                Imblackcolorphase = -Im;
                Imblackcolorphase(Imblackcolorphase>=0) = 1;
                Imblackcolorphase(Imblackcolorphase<0) = 0;

                % Half cycle of grating
                ImRwhitecolorphase = Imwhitecolorphase*rgain;
                ImGwhitecolorphase = Imwhitecolorphase*ggain;
                ImBwhitecolorphase = Imwhitecolorphase*bgain;

                % The other half cycle of grating
                ImRblackcolorphase = Imblackcolorphase*blackcolor(1);
                ImGblackcolorphase = Imblackcolorphase*blackcolor(2);
                ImBblackcolorphase = Imblackcolorphase*blackcolor(3);

                %Merge
                ImRdum = ImRwhitecolorphase + ImRblackcolorphase;
                ImGdum = ImGwhitecolorphase + ImGblackcolorphase;
                ImBdum = ImBwhitecolorphase + ImBblackcolorphase;
                
                ImRGB(:,:,1) = round(ImRdum);  %[0 255]
                ImRGB(:,:,2) = round(ImGdum);
                ImRGB(:,:,3) = round(ImBdum);
                
%                 ImRGB(:,:,1) = round(ImRdum*255);  %[0 255]
%                 ImRGB(:,:,2) = round(ImGdum*255);
%                 ImRGB(:,:,3) = round(ImBdum*255);
            end
        
        %     Gtx(:,:,:,i) = ImRGB;
            writeVideo(vidfile, ImRGB);
        
            %% Save data and figures
        
            if plotModulation == 1
                % Save color lut for future use
                colors = [ImRdum; ImGdum; ImBdum]';
                save([resultFolder, imgName, '_lut.mat'], 'colors', '-v7.3');
        
                if colormod == 1
        
                    figure;  % Save modulation diagram
                    plot(ImRdum, '-', 'LineWidth', 5, 'Color',[0.5 0.5 0.5]);
                    hold on;
                    plot(ImGdum, '-', 'LineWidth', 5, 'Color',[0.5 0.5 0.5]);
                    hold on;
                    plot(ImBdum, '-', 'LineWidth', 5, 'Color',[0.5 0.5 0.5]);
                    hold on;
                    yline(0.5, '--', 'LineWidth', 3, 'Color',[0.5 0.5 0.5]);
                else
                   figure;  % Save modulation diagram
                    plot(ImRdum, '-', 'LineWidth', 5, 'Color',[1 0 0]);
                    hold on;
                    plot(ImGdum, '-', 'LineWidth', 5, 'Color',[0 1 0]);
                    hold on;
                    plot(ImBdum, '-', 'LineWidth', 5, 'Color',[0 0 1]);
                    hold on;
                    yline(0.5, '--', 'LineWidth', 3, 'Color',[0.5 0.5 0.5]);
                end
        
                ax = gca;
                set(ax,'xtick',[])
                set(ax,'xticklabel',[])
                set(ax,'ytick',[])
                set(ax,'yticklabel',[])
                axis off
                % axis square
                % box on
                export_fig (gcf, [resultFolder, imgName, '_modulation.png'])
            else
        
        %             figure   % Save color gratings
        %             imshow(ImRGB)
        %             export_fig (gcf, [resultFolder, imgName, '_', num2str(ori), '_', num2str(s_freq), '_grating.png'])
        
            end
        end   
    
end
close(vidfile);
close all


%%

% % cc=linspace(0,1,360)';
% % colors=[cc,cc,cc];
% 
% figure
% colormap(lut);
% 
% dd=linspace(1,0,360)';
% ee=repmat(dd,1,50);
% % imshow(ee)
% imagesc(ee)
% imwrite(ee,'jetbar.tif')
% 
% % figure
% % colormap(colors);
% % colorbar
% ax = gca;
% set(ax,'xtick',[])
% set(ax,'xticklabel',[])
% set(ax,'ytick',[])
% set(ax,'yticklabel',[])
% axis off
% saveas(gcf, ['Jet_bars.tif'])
