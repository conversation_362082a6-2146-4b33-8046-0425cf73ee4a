import numpy as np
import pandas as pd
import scipy as sp
from scipy import signal
import scipy.optimize as opt
import matplotlib.pyplot as plt
from matplotlib.patches import Patch
from matplotlib.colors import ListedColormap, LinearSegmentedColormap
import scipy.io as sio
import glob
import os
import csv
import pickle
import math
import hdf5storage
import h5py
from shapely.geometry.polygon import Polygon
from descartes import PolygonPatch


# Add details about imaging experiment (unit, expt, plane)
disks = ['K:', 'G:']
animals = ['AE6', 'AE7']   # AF4, AF3
# unit1 = ['002'] # AE6
# unit2 = ['003','004']  # AE6
allcases = {
    0: ['AE6_002_001', 'AE6_003_000','AE6_003_000','AE6_004_000','AE6_004_001'],
    1: ['AE7_005_000']
    }


planeId = ['000','001'] #'001'   #'000'
thres = 0.25
hueaucThres = 0.8

numberofColor = 12
hueList = [0, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330]
colorSpace = 'HSL'

# plot_dominance = False
# plot_onoff = False
# singleplane = False
# plot_diamond = True
white_BK = True    # True for white/gray background, False for aligned 2P image background
# BK_alpha = 0.5     # background alpha 
font_size = 15
circle_size = 3
ylimt_hist = 10

## Define the color map for ploting
colormappath = 'C:\\Users\\<USER>\\.julia\\dev\\NeuroAnalysis\\src\\Visualization\\colors'
ori_lut = ListedColormap(sio.loadmat(os.path.join(colormappath, 'ori_lut_alpha0.mat'), squeeze_me=True, struct_as_record=False)['lut'])
# cmap_patch = plt.cm.get_cmap('hsv')
cmap_patch_sf = plt.cm.get_cmap('jet')
cmap_patch_cpi = plt.cm.get_cmap('jet')
cmap_patch_osi = plt.cm.get_cmap('jet')
cmap_patch_uniquehue = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'uniquehue.mat'), squeeze_me=True, struct_as_record=False)['lut'])
cmap_patch_hsl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_hsl_mshue_l0.4.mat'), squeeze_me=True, struct_as_record=False)['colors'])
cmap_patch_dkl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_dkl_mcchue_l0.mat'), squeeze_me=True, struct_as_record=False)['colors'])
cmap_patch_lidkl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_lidkl_mcchue_l0.mat'), squeeze_me=True, struct_as_record=False)['colors'])
if colorSpace  == 'DKL':
    color_hex_keys = ListedColormap(['#FF8080', '#FF80BF', '#FF80FF', '#BF80FF', '#8080FF', '#80BFFF', '#80FFFF',
            '#80FFBF', '#80FF80', '#BFFF80', '#FFFF80', '#FFBF80', '#808080'])   # DKL hues
elif colorSpace == 'HSL':
    color_hex_keys = ListedColormap(['#af1600', '#8a4600', '#5a5d01', '#2a6600', '#006a00', '#006931', '#006464',
            '#0058b6', '#002DFF', '#6a2ade', '#97209b', '#aa1c50', '#808080'])   # HSL hues
gray = '#B4B4B4'    # '#808080'


# Make folders for saving data from all units
result_path_allcase = os.path.join('G:', 'AE7', '2P_analysis', 'Summary', 'Plots', 'STA','Allcase')
if not os.path.exists(result_path_allcase):
    os.mkdir(result_path_allcase)


# For storage
cellNum = 0
LMScellNum = 0
sOnCellNum = 0
sOffCellNum = 0
sOnCell = []
sOffCell = []

# Collect data
fig, ax = plt.subplots()
for i, animal in enumerate(animals):
    print('Plot Cone Weights of cells have LMS STA......')

    disk = disks[i]
    cases = allcases[i]
    main_path = os.path.join(disk, animal, '2P_analysis')

    # for j, unit in enumerate(units):
    #     print('Processing Unit %s.' % unit)
    for j, case in enumerate(cases):
        print('Processing Case %s.' % case)

        unit = case[4:7]
        plane = case[8:11]

        unit_path = os.path.join(main_path, 'U%s' % unit)
        planeData_path = os.path.join(unit_path,'_Summary', 'DataExport') # has data organized in plane dimension

        ## Load cone weights data
        sta_file = os.path.join(planeData_path,'%s_%s_thres%s_sta_dataset.csv' %(animal, unit, thres))
        sta = pd.read_csv(sta_file, index_col=0)

        cellId = sta.cellId.values
        plId = sta.planeId.values
        lcw = sta.lcwmn.values
        mcw = sta.mcwmn.values
        scw = sta.scwmn.values
        isl = sta.isl.values
        ism = sta.ism.values
        iss = sta.iss.values

        # lcw = sta.lcwmg.values
        # mcw = sta.mcwmg.values
        # scw = sta.scwmg.values

        # lcw = sta.lcwmgall.values
        # mcw = sta.mcwmgall.values
        # scw = sta.scwmgall.values

        # for pl in range(np.size(planeId)):

        # plane = planeId[pl]
        # load hue data
        # data_file = os.path.join(planeData_path,'%s_%s_%s_sum.csv' %(animal, unit, plane))
        data_file = os.path.join(planeData_path,'%s_sum.csv' %(case))
        plda = pd.read_csv(data_file, index_col=0)
        max_hue = plda.maxhue
        visResp = plda.visResp
        hue_ax_auc = plda.hueaxauc
        hue_di_auc = plda.huediauc

        for i in range(len(cellId)):
            if (plId[i]==int(plane)) & (~(math.isnan(lcw[i])) | ~(math.isnan(mcw[i]))):
                if visResp[cellId[i] - 1]>0:
                    cellNum += 1
                    if ((hue_ax_auc[cellId[i] - 1] >hueaucThres) | (hue_di_auc[cellId[i] - 1]>hueaucThres)): #& (isl[i]) & (ism[i]):
                    # if  (isl[i]) & (ism[i]):
                        LMScellNum +=1
                        if colorSpace == 'DKL':
                            plt.scatter(lcw[i], mcw[i], s=circle_size, alpha=1, color=cmap_patch_lidkl(max_hue[cellId[i]-1]/360))
                        elif colorSpace == 'HSL':
                            plt.scatter(lcw[i], mcw[i], s=circle_size, alpha=1, color=color_hex_keys(hueList.index(max_hue[cellId[i] - 1])))
                    # elif (~ism[i] | ~isl[i]):
                    #     if colorSpace == 'DKL':
                    #         plt.scatter(lcw[i], mcw[i], s=circle_size, alpha=0.2, color=cmap_patch_lidkl(max_hue[cellId[i]-1]/360))
                    #     elif colorSpace == 'HSL':
                    #         plt.scatter(lcw[i], mcw[i], s=circle_size, alpha=0.2, color=color_hex_keys(hueList.index(max_hue[cellId[i] - 1])))
                    else:
                        plt.scatter(lcw[i], mcw[i], s=circle_size, alpha=0.4, color='gray')

            # if (plId[i] == int(plane)) & (np.abs(lcw[i]) + np.abs(mcw[i]) < 0.5):
            if (plId[i] == int(plane)) & (iss[i]):
                if ((hue_ax_auc[cellId[i] - 1] > hueaucThres) | (hue_di_auc[cellId[i] - 1] > hueaucThres)) & (visResp[cellId[i] - 1] > 0):
                    if scw[i] >= 0:  # S-on
                        sOnCellNum += 1
                        sOnCell.append(max_hue[cellId[i] - 1])
                    elif scw[i] < 0:  # S-off
                        sOffCellNum += 1
                        sOffCell.append(max_hue[cellId[i] - 1])        


#### PLot figures ############################

#### Diamond plot
file_name_cw = os.path.join('%s_thres%s_allplane_cone weight' % (''.join(animals), thres))

plt.plot([0, 0], [1, 0], 'k-', lw=0.5, linestyle='--')
plt.plot([0, 0], [-1, 0], 'k-', lw=0.5, linestyle='--')
plt.plot([0, -1], [0, 0], 'k-', lw=0.5, linestyle='--')
plt.plot([0, 1], [0, 0], 'k-', lw=0.5, linestyle='--')
plt.plot([-1, 0], [0, 1], 'k-', lw=0.5, linestyle='-')
plt.plot([-1, 0], [0, -1], 'k-', lw=0.5, linestyle='-')
plt.plot([1, 0], [0, 1], 'k-', lw=0.5, linestyle='-')
plt.plot([1, 0], [0, -1], 'k-', lw=0.5, linestyle='-')

plt.ylabel('Normalized M-cone Strength', fontsize=font_size)
plt.xlabel('Normalized L-cone Strength', fontsize=font_size)
plt.ylim(-1, 1)
plt.xlim(-1, 1)
ax.set_xticks([-1, 0, 1])
ax.set_yticks([-1, 0, 1])
ax.set_aspect('equal', 'box')

plt.savefig('%s/%s_CellNum%s_mn_aucThres%s_L&M&S%s_LMScell.svg' % (result_path_allcase, file_name_cw,cellNum,hueaucThres,LMScellNum), dpi=300, format='svg')
plt.savefig('%s/%s_CellNum%s_mn_aucThres%s_L&M&S%s_LMScell.png' % (result_path_allcase, file_name_cw,cellNum,hueaucThres,LMScellNum), dpi=300, bbox_inches='tight', pad_inches=0,format='png')
# plt.show()


#### PLot Hue Histogram of cells have S inputs

## S-on Hist
fig, ax = plt.subplots()
histPatch = pd.Series(sOnCell).plot(kind='hist', bins=12, rwidth=0.8)
# Set colors on hist patches
for patches in range(0, 12):
    if colorSpace == 'DKL':
        histPatch.patches[patches].set_color(cmap_patch_lidkl(patches/12))
    elif colorSpace == 'HSL':
        histPatch.patches[patches].set_color(color_hex_keys(patches))
    # histPatch.patches[patches].set_color(cmap_patch_hue1(patches/36))

# ax.axes.get_xaxis().set_visible(False)
plt.tick_params(
    axis='x',  # changes apply to the x-axis
    labelsize='large',
    which='both',  # both major and minor ticks are affected
    bottom=False,  # ticks along the bottom edge are off
    top=False,  # ticks along the top edge are off
    labelbottom=False)  # labels along the bottom edge are off
plt.ylabel('Cell Number', fontsize=font_size)
plt.xlabel('Preferred Hue', fontsize=font_size)
plt.ylim(0, ylimt_hist)  # 380
# plt.grid(b=True, which='major', axis='y', alpha = 0.5, linewidth=1)
ax.set_axisbelow(True)

plt.savefig('%s/%s_cellNum%s_aucThres%s_Son_hist.svg' % (result_path_allcase, file_name_cw, sOnCellNum,hueaucThres),dpi=300, format='svg')
plt.savefig('%s/%s_cellNum%s_aucThres%s_Son_hist.png' % (result_path_allcase, file_name_cw, sOnCellNum,hueaucThres),dpi=300, bbox_inches='tight', pad_inches=0, format='png')
# plt.show()


## S-off hist
fig, ax = plt.subplots()
histPatch = pd.Series(sOffCell).plot(kind='hist', bins=12, rwidth=0.8)
# Set colors on hist patches
for patches in range(0, 12):
    if colorSpace == 'DKL':
        histPatch.patches[patches].set_color(cmap_patch_lidkl(patches/12))
    elif colorSpace == 'HSL':
        histPatch.patches[patches].set_color(color_hex_keys(patches))

# ax.axes.get_xaxis().set_visible(False)
plt.tick_params(
    axis='x',  # changes apply to the x-axis
    labelsize='large',
    which='both',  # both major and minor ticks are affected
    bottom=False,  # ticks along the bottom edge are off
    top=False,  # ticks along the top edge are off
    labelbottom=False)  # labels along the bottom edge are off
plt.ylabel('Cell Number', fontsize=font_size)
plt.xlabel('Preferred Hue', fontsize=font_size)
plt.ylim(0, ylimt_hist)  # 380
# plt.grid(b=True, which='major', axis='y', alpha = 0.5, linewidth=1)
ax.set_axisbelow(True)

plt.savefig('%s/%s_cellNum%s_aucThres%s_Soff_hist.svg' % (result_path_allcase, file_name_cw, sOffCellNum,hueaucThres),dpi=300, format='svg')
plt.savefig('%s/%s_cellNum%s_aucThres%s_Soff_hist.png' % (result_path_allcase, file_name_cw, sOffCellNum,hueaucThres), dpi=300,bbox_inches='tight', pad_inches=0, format='png')
# plt.show()
