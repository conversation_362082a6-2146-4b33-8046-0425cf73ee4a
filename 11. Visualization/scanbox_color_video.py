import numpy as np
import scipy.io as sio
import functions
import scanbox_analysis
from skimage.restoration import denoise_tv_chambolle, estimate_sigma
import matplotlib.pyplot as plt
import matplotlib.animation as manimation
import matplotlib.patches as patches
from scipy import ndimage
plt.switch_backend('Agg')

# Add details about imaging experiment (unit and trial)
animal = 'AE5'
unit = '006'
trial = '007'
folder = '/Volumes/lacie/NHP data'

# Hex values of stimulus colors
color_hex = ['#af1600', '#8a4600', '#5a5d01', '#2a6600', '#006a00', '#006931', '#006464',
             '#0058b6', '#002DFF', '#6a2ade', '#97209b', '#aa1c50', '#ffffff']
color_selective = []

# Load all relevant files to the imaging file
sbx_path = '%s/%s/2p_data/%s_%s_%s_rigid.sbx' % (folder, animal, animal, unit, trial)
file_path = '%s/%s/2p_data/%s_%s_%s.signals' % (folder, animal, animal, unit, trial)
analyzer_path = '%s/%s/AnalyzerFiles/%s_u%s_%s.analyzer' % (folder, animal, animal, unit, trial)
info_file = "%s/%s/2p_data/%s_%s_%s.mat" % (folder, animal, animal, unit, trial)
ref_path = "%s/%s/2p_data/%s_%s_%s.ref" % (folder, animal, animal, unit, trial)
align_path = "%s/%s/2p_data/%s_%s_%s.align" % (folder, animal, animal, unit, trial)
segment_file = "%s/%s/2p_data/%s_%s_%s.segment" % (folder, animal, animal, unit, trial)
plot = False  # Plot individual tuning curves?
plot_layers = False  # Plot multiple layers (see bottom of code)
plot_gaussian_fit = False  # Plot Gaussian fits of tuning curve (testing)

F = sio.loadmat(file_path, squeeze_me=True, struct_as_record=False)['sig']
info = sio.loadmat(info_file, squeeze_me=True, struct_as_record=False)['info']
segment = sio.loadmat(segment_file, squeeze_me=True, struct_as_record=False)['vert']
ref = sio.loadmat(ref_path, squeeze_me=True, struct_as_record=False)['ref']
align = sio.loadmat(align_path, squeeze_me=True, struct_as_record=False)['m']
align_baseline = np.mean(align[0:100, 0:10])
params = functions.analyzer_params(analyzer_path)
trial_num, stim_time = functions.analyzer_pg_conds(analyzer_path)
trial_num['direction'] = trial_num.ori
trial_num.ori[(trial_num.ori >= 180)] = trial_num.ori[(trial_num.ori >= 180)] - 180

zoom = info.config.magnification_list[info.config.magnification - 1]

# Correct error for experiments that have more than 65536 frames
info.frame = info.frame.astype(int)
frame_diff = np.diff(info.frame)
frame_reset = np.where(frame_diff < 0)
for reset in frame_reset[0]:
    info.frame[(reset+1):] += 65536

# Correct error if first frame/line is 0
if info.frame[0] == 0:
    info.frame = info.frame[1:]
    info.line = info.line[1:]

# Load start and end frame numbers and calculate trial length in frames
start_frames = info.frame[0::2]
end_frames = info.frame[1::2]
trial_length = np.min(end_frames - start_frames)

# Build array of averaged color responses
col_avg = np.zeros([796, 512, trial_length, len(trial_num.colormod.unique())])

for j, colormod in enumerate(np.sort(trial_num.colormod.unique())):
    trial_num_color = trial_num[trial_num.colormod == colormod]
    print('Processing color %d of %d.' % (j+1, len(trial_num.colormod.unique())))
    for i, trial_idx in enumerate(trial_num_color.index):
        print('Processing trial %d of %d.' % (i+1, len(trial_num_color.index)))
        col_avg_ind = np.zeros([796, 512, trial_length])
        col_avg[:, :, :, j] += scanbox_analysis.sbxread(sbx_path, k=start_frames[trial_idx], N=trial_length).squeeze()
    col_avg[:, :, :, j] = col_avg[:, :, :, j] / trial_num_color.shape[0]

np.save('/Volumes/lacie/NHP data/movie_arr.npy', col_avg)

# Build array of averaged color responses for single cell
cell = 113  # or use 253
col_avg_cell = np.zeros([trial_length, len(trial_num.colormod.unique())])

for j, colormod in enumerate(np.sort(trial_num.colormod.unique())):
    trial_num_color = trial_num[trial_num.colormod == colormod]
    print('Processing color %d of %d.' % (j+1, len(trial_num.colormod.unique())))
    for i, trial_idx in enumerate(trial_num_color.index):
        col_avg_ind = np.zeros([796, 512, trial_length])
        col_avg_cell[:, j] += F[start_frames[trial_idx]:start_frames[trial_idx]+trial_length, cell]
    col_avg_cell[:, j] = col_avg_cell[:, j] / trial_num_color.shape[0]

# Calculate baseline
F_0 = np.mean(col_avg_cell[0:int(trial_length * stim_time[0] / np.sum(stim_time)), :])
df_f = (col_avg_cell - F_0) / F_0

# Create movie of entire imaging region
col_avg = np.load('/Volumes/lacie/NHP data/movie_arr.npy')

# setup mpeg writer
plot_rect = True
rect_x = 250
rect_xw = 150
rect_y = 75
rect_yw = 150

plt.switch_backend('Agg')
framerate = 60  # fps
save_folder = '/Volumes/lacie/NHP data/'

title = 'grater'
artist = 'anupam'
comment = 'created with matplotlib'

FFMpegWriter = manimation.writers['ffmpeg']
metadata = dict(title=title, artist=artist, comment=comment)
writer = FFMpegWriter(fps=framerate, metadata=metadata)

fig = plt.figure(frameon=False)
fig.set_size_inches(796/fig.dpi, 512/fig.dpi)
ax = fig.add_axes([0, 0, 1, 1])
ax.axis('off')
if plot_rect:
    rect = patches.Rectangle((rect_x, rect_y), rect_xw, rect_yw, linewidth=1, edgecolor='w', facecolor='none')
    ax.add_patch(rect)
l = plt.imshow(ndimage.rotate(col_avg[:, :, 0, 0], 90), origin='lower', cmap='gray')
plt.show()

stim_time_prop = stim_time / np.sum(stim_time)

with writer.saving(fig, save_folder + "color_movie_%dfps.mp4" % framerate, 300):
    for j in np.arange(col_avg.shape[3]):
        print('Processing color %d.' % (j+1))
        rect2 = patches.Rectangle((0, 0), 50, 50, linewidth=1, edgecolor=color_hex[j], facecolor=color_hex[j])
        rect2.set_visible(False)
        ax.add_patch(rect2)
        for i in np.arange(col_avg.shape[2]):
            if (i > stim_time_prop[0] * col_avg.shape[2]) & \
                    (i < (col_avg.shape[2] - (stim_time_prop[1] * col_avg.shape[2]))):
                rect2.set_visible(True)
                image = col_avg[:, :, i, j]
                image = ndimage.rotate(image, 90)
                l.set_data(image)
                # l.set_data(denoise_tv_chambolle(image, weight=500, multichannel=False))
                writer.grab_frame()
            else:
                rect2.set_visible(False)
                image = col_avg[:, :, i, j]
                image = ndimage.rotate(image, 90)
                l.set_data(image)
                # l.set_data(denoise_tv_chambolle(image, weight=500, multichannel=False))
                writer.grab_frame()
plt.close()

# Create movie of zoomed-in region
# setup mpeg writer
plt.switch_backend('Agg')
framerate = 60  # fps
save_folder = '/Volumes/lacie/NHP data/'

title = 'grater'
artist = 'anupam'
comment = 'created with matplotlib'

FFMpegWriter = manimation.writers['ffmpeg']
metadata = dict(title=title, artist=artist, comment=comment)
writer = FFMpegWriter(fps=framerate, metadata=metadata)

fig = plt.figure(frameon=False)
fig.set_size_inches(rect_xw/fig.dpi, rect_yw/fig.dpi)
ax = fig.add_axes([0, 0, 1, 1])
ax.set_xlim(rect_x, rect_x+rect_xw)
ax.set_ylim(rect_y, rect_y+rect_yw)
ax.axis('off')
l = plt.imshow(ndimage.rotate(col_avg[:, :, 0, 0], 90),
               origin='lower', cmap='gray')

stim_time_prop = stim_time / np.sum(stim_time)

with writer.saving(fig, save_folder + "color_movie_%dfps_zoom.mp4" % framerate, 300):
    for j in np.arange(col_avg.shape[3]):
        print('Processing color %d.' % (j+1))
        # rect2 = patches.Rectangle((0, 0), 50, 50, linewidth=1, edgecolor=color_hex[j], facecolor=color_hex[j])
        # rect2.set_visible(False)
        # ax.add_patch(rect2)
        for i in np.arange(col_avg.shape[2]):
            # rect2.set_visible(True)
            image = col_avg[:, :, i, j]
            image = ndimage.rotate(image, 90)
            l.set_data(image)
            l.set_clim(vmin=col_avg[:, :, i, j].min(), vmax=col_avg[:, :, i, j].max())
            writer.grab_frame()
plt.close()


# Create movie of individual cell traces
# setup mpeg writer #2
plt.switch_backend('Agg')
framerate = 60  # fps
save_folder = '/Volumes/lacie/NHP data/'

title = 'cell response'
artist = 'anupam'
comment = 'created with matplotlib'

FFMpegWriter = manimation.writers['ffmpeg']
metadata = dict(title=title, artist=artist, comment=comment)
writer = FFMpegWriter(fps=framerate, metadata=metadata)

fig, ax = plt.subplots(frameon=True)
fig.set_size_inches(796/fig.dpi, 512/fig.dpi)
ax.set_xlim(0, int(np.sum(stim_time)))
ax.set_ylim(0, df_f.max() + 0.2)
ax.axvspan(stim_time[0], np.sum(stim_time) - stim_time[1], alpha=0.3, color='gray')
ax.text(0.5, 0.95, 'Stimulus', horizontalalignment='center', transform=ax.transAxes)
ax.set_xlabel('Time (s)')
ax.set_ylabel(r'Response ($\Delta F/F$)')
l, = plt.plot([], [])
t = np.linspace(0, int(np.sum(stim_time)), col_avg_cell.shape[0])

stim_time_prop = stim_time / np.sum(stim_time)

with writer.saving(fig, save_folder + "color_movie_traces_%dfps_%d.mp4" % (framerate, cell), 300):
    for j in np.arange(col_avg_cell.shape[1]):
        print('Processing color %d.' % (j+1))
        for i in np.arange(col_avg_cell.shape[0]):
            data = df_f[0:i, j]
            ax.plot(t[0:i], data, color=color_hex[j])
            writer.grab_frame()
plt.close()

plt.switch_backend('MacOSX')
fig, ax = plt.subplots()
ax.imshow(ndimage.rotate(col_avg[:, :, 0, 0], 90), cmap='gray', origin='lower')
rect = patches.Rectangle((250,75),150,150,linewidth=1,edgecolor='r',facecolor='none')
ax.add_patch(rect)
plt.show()

plt.switch_backend('MacOSX')
fig, ax = plt.subplots()
ax.imshow(ndimage.rotate(col_avg[rect_x:rect_x+rect_xw, rect_y:rect_y+rect_yw, 0, 0], 90),
          origin='lower', cmap='gray')
plt.show()
