import numpy as np
import pandas as pd
import scipy as sp
from scipy import signal
import scipy.optimize as opt
import matplotlib.pyplot as plt
from matplotlib.patches import Patch
from matplotlib.colors import ListedColormap, LinearSegmentedColormap
import scipy.io as sio
import glob
import os
import re
import csv
import pickle
import math
import hdf5storage
import h5py
from shapely.geometry.polygon import Polygon
from descartes import PolygonPatch


# Add details about imaging experiment (unit, expt, plane)
disk = '/media/vcl/vcl003/'
animal = 'AF3'   # AF4, AF3
# units = ['002','003','004']  # AE6
# units = ['001', '003','005','006','008','009','011','012','013','014']  # AE7
units = ['002']
# units = ['002', '003','004','005','006']  # AF4
expt_hartley = ['L-cone','M-cone','S-cone','Achromatic']
expt_cone_drift = {'010': 'L-cone',
                   '011': 'M-cone',
                   '012': 'S-cone',
                   '005': 'Achromatic'}
planeId = ['001'] #'001'   #'000'
thres = 0.25
# Check which cell is selective
oriaucThres = 0.7
diraucThres = 0.7
hueaucThres = 0.8

numberofColor = 12
hueList = [0, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330]
colorSpace = 'DKL'

plot_SFtype_Hartley = False
plot_cellType_Drift = True
plot_ori_Hartley = False
singleplane = True

white_BK = True    # True for white/gray background, False for aligned 2P image background
BK_alpha = 0.5     # background alpha
font_size = 15
circle_size = 3
ylimt_hist = 80

## Define the color map for ploting
colormappath = '/home/<USER>/Documents/Gitee/Experica/items_2P_RECON_tz/2p/11. Visualization/ColorLut/'
ori_lut = ListedColormap(sio.loadmat(os.path.join(colormappath, 'ori_lut_alpha0.mat'), squeeze_me=True, struct_as_record=False)['lut'])
# cmap_patch = plt.cm.get_cmap('hsv')
cmap_patch_sf = plt.cm.get_cmap('jet')
cmap_patch_cpi = plt.cm.get_cmap('jet')
cmap_patch_osi = plt.cm.get_cmap('jet')
cmap_patch_pw = plt.cm.get_cmap('jet')
cmap_patch_uniquehue = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'uniquehue.mat'), squeeze_me=True, struct_as_record=False)['lut'])
cmap_patch_hsl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_hsl_mshue_l0.4.mat'), squeeze_me=True, struct_as_record=False)['colors'])
cmap_patch_dkl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_dkl_mcchue_l0.mat'), squeeze_me=True, struct_as_record=False)['colors'])
cmap_patch_lidkl = ListedColormap(hdf5storage.loadmat(os.path.join(colormappath,'cm_lidkl_mcchue_l0.mat'), squeeze_me=True, struct_as_record=False)['colors'])
if colorSpace  == 'DKL':
    color_hex_keys = ListedColormap(['#FF8080', '#FF80BF', '#FF80FF', '#BF80FF', '#8080FF', '#80BFFF', '#80FFFF',
            '#80FFBF', '#80FF80', '#BFFF80', '#FFFF80', '#FFBF80', '#808080'])   # DKL hues
elif colorSpace == 'HSL':
    color_hex_keys = ListedColormap(['#af1600', '#8a4600', '#5a5d01', '#2a6600', '#006a00', '#006931', '#006464',
            '#0058b6', '#002DFF', '#6a2ade', '#97209b', '#aa1c50', '#808080'])   # HSL hues
gray = '#B4B4B4'    # '#808080'
sfTypeCmap = {"L": "o",
                "B": "^",
                "H": "^"}
cellTypeCmap = {"I": ".", "II": "o", "III": "^"}

main_path = os.path.join(disk, animal, '2P_analysis/OriginalResults')
featureData_path = os.path.join(main_path, 'Summary', 'DataExport')  # has ROIs,Bkgs, and data organized in feature dimenstion
result_path_allunit0 = os.path.join(main_path, 'Summary', 'Plots')
result_path_allunit = os.path.join(result_path_allunit0,'Fourier')

# Make folders for saving data from all units
if not os.path.exists(result_path_allunit0):
    os.mkdir(result_path_allunit0)
if not os.path.exists(result_path_allunit):
    os.mkdir(result_path_allunit)


## plotting SF type map
if plot_SFtype_Hartley:

    for j, unit in enumerate(units):
        print('Processing Hartley Unit %s.' % unit)

        unit_path = os.path.join(main_path, 'U%s' % unit)
        planeData_path = os.path.join(unit_path,'_Summary', 'DataExport') # has data organized in plane dimension
        result_path_plane = os.path.join(unit_path,'_Summary', 'Multiplane')
        result_path_multiplane = os.path.join(result_path_plane,'0. Original maps')

        result = {}
        for pl in range(np.size(planeId)):
            result[planeId[pl]] = np.array([])

        # Make folders for saving data from single unit
        if not os.path.exists(result_path_plane):
            os.mkdir(result_path_plane)
        if not os.path.exists(result_path_multiplane):
            os.mkdir(result_path_multiplane)

        ## Load cone significance data
        fdata_file = os.path.join(
            planeData_path,
            '%s_%s_thres0.25_fourier_dataset_organized.csv' % (animal, unit))
        fdata = pd.read_csv(fdata_file, index_col=0)
        cellId = fdata.cellId.values
        plId = fdata.planeId.values
        isl = fdata.isl.values
        ism = fdata.ism.values
        iss = fdata.iss.values
        isa = fdata.isa.values

        lpsf = fdata.lpsf.values
        mpsf = fdata.mpsf.values
        spsf = fdata.spsf.values
        apsf = fdata.apsf.values

        lsft = fdata.lsft.values
        msft = fdata.msft.values
        ssft = fdata.ssft.values
        asft = fdata.asft.values

        lsfpw = fdata.lsfpw.values
        msfpw = fdata.lsfpw.values
        ssfpw = fdata.lsfpw.values
        asfpw = fdata.lsfpw.values

        numCell = np.size(cellId)

        for i, stim in enumerate(expt_hartley):
            print('Processing %s stimulus.' % stim)
            file_name_allplane = os.path.join('%s_u%s_%s_thres%s_allplane' % (animal, unit, stim, thres))
            if stim == 'L-cone':
                cs = isl
                type_sf = lsft
                sfpw = lsfpw
                psf = lpsf
            elif stim == 'M-cone':
                cs = ism
                type_sf = msft
                sfpw = msfpw
                psf = mpsf
            elif stim == 'S-cone':
                cs = iss
                type_sf = ssft
                sfpw = ssfpw
                psf = spsf
            elif stim == 'Achromatic':
                cs = isa
                type_sf = asft
                sfpw = asfpw
                psf = apsf

            if singleplane:

                for pl in range(np.size(planeId)):

                    plane = planeId[pl]

                    # load hue data
                    data_file = os.path.join(planeData_path,'%s_%s_%s_sum.csv' %(animal, unit, plane))
                    # data_file = os.path.join(planeData_path, '%s_%s_%s_oriData.csv' % (animal, unit, plane))
                    plda = pd.read_csv(data_file, index_col=0)
                    visResp = plda.visResp
                    xloc=plda.xloc-1
                    yloc=plda.yloc-1


                    print('Processing plane: '+plane)
                    result_path_singleplane = os.path.join(unit_path,'_Summary', 'plane_%s' %(plane), '0. Original maps')
                    file_name_singleplane = os.path.join('%s_u%s_plane%s_%s_thres%s_singleplane' % (animal, unit, plane, stim, thres))
                    ## load background images and ROIs
                    # I use images from orisf_achromatic data as background image. All maps are plotted on this image.
                    roibkg_file = os.path.join(planeData_path,'%s_%s_%s_roibkg.jld2' %(animal, unit, plane))
                    # roibkg_file = os.path.join(planeData_path, 'AE6_002_011_roibkg.jld2')   # Sometimes, there was z-shift between Hartley and ori/hue, so the ROIs are different.
                    roibkg = h5py.File(roibkg_file, 'r+')
                    bkgimg = np.transpose(roibkg['bkg'][()])
                    whitBK = np.ones(bkgimg.shape)*0.8

                    # load roi segements/contours
                    rois = roibkg["roi"][()]

                    # Transform roi contours to patches
                    allROIs = []   # patches of all ROIs
                    for i in range(np.size(rois)):
                        allROIs.append(0)    # append 0 if there is ROI
                        #Find the segment/contour for this cell
                        roibkg[rois[i]][()] = roibkg[rois[i]][()].astype(int)
                        allROIs[i] = Polygon([tuple(l) for l in list(np.transpose(roibkg[rois[i]][()]-1))])

                    ## Initialize plotting
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')

                    for vi in np.arange(numCell):

                        if plId[vi]==int(plane):

                            if visResp[cellId[vi]-1]>0:
                                if cs[vi]>0:  # cone significant

                                    # ax.add_patch(PolygonPatch(allROIs[cellId[vi] - 1], alpha=1, color='black' ))
                                    ax.scatter(xloc[cellId[vi] - 1],
                                               yloc[cellId[vi] - 1],
                                               s=5,
                                               color='gray',
                                               marker=sfTypeCmap[type_sf[vi]])
                                    # ax.text(xloc[cellId[vi]-1],yloc[cellId[vi]-1], str(cellId[vi]),horizontalalignment='center', fontsize=3)   # add cellId
                                    result[plane] = np.hstack((result[plane], type_sf[vi]))
                                else:
                                    result[plane] = np.hstack((result[plane], 'NaN'))
                            else:
                                result[plane] = np.hstack((result[plane], 'NaN'))

                    ax.set_rasterized(True)
                    ax.set_xticklabels([])
                    ax.set_yticklabels([])
                    ax.minorticks_off()
                    ax.set_frame_on(False)
                    plt.grid(True)
                    plt.grid(color='gray', linestyle='--', linewidth=0.5, alpha=0.3)

                    # plt.axis('off')
                    plt.savefig('%s/%s_hartley_SFtype.svg' % (result_path_singleplane, file_name_singleplane), dpi=300, bbox_inches='tight', pad_inches=0, format='svg')
                    plt.savefig('%s/%s_hartley_SFtype.png' % (result_path_singleplane, file_name_singleplane), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    # plt.show()

                    ## Initialize plotting
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK,
                                  cmap='gray',
                                  alpha=BK_alpha,
                                  vmin=0,
                                  vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')

                    for vi in np.arange(numCell):

                        if plId[vi] == int(plane):

                            if visResp[cellId[vi] - 1] > 0:
                                ax.add_patch(
                                    PolygonPatch(allROIs[cellId[vi] - 1],
                                                 alpha=1,
                                                 color=gray))
                                if cs[vi] > 0:  # cone significant
                                    ax.add_patch(PolygonPatch(allROIs[cellId[vi] - 1],alpha=psf[vi]/np.max(psf)*0.7+0.3,color=cmap_patch_pw(sfpw[vi]/np.max(sfpw))))
                                    # ax.text(xloc[cellId[vi]-1],yloc[cellId[vi]-1], str(cellId[vi]),horizontalalignment='center', fontsize=3)   # add cellId
                    ax.set_rasterized(True)
                    ax.set_xticklabels([])
                    ax.set_yticklabels([])
                    ax.minorticks_off()
                    ax.set_frame_on(False)
                    plt.grid(True)
                    plt.grid(color='gray',
                             linestyle='--',
                             linewidth=0.5,
                             alpha=0.3)

                    # plt.axis('off')
                    plt.savefig(
                        '%s/%s_hartley_SFpw.svg' %
                        (result_path_singleplane, file_name_singleplane),
                        dpi=300,
                        bbox_inches='tight',
                        pad_inches=0,
                        format='svg')
                    plt.savefig(
                        '%s/%s_hartley_SFpw.png' %
                        (result_path_singleplane, file_name_singleplane),
                        dpi=300,
                        bbox_inches='tight',
                        pad_inches=0,
                        format='png')
                    # plt.show()

                    # fig, ax = plt.subplots()
                    # ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    # legend_elements = [Patch(facecolor='b', edgecolor='b', label=stim+' OFF'),
                    #                 Patch(facecolor='r', edgecolor='r', label=stim+' ON')]
                    # ax.legend(handles=legend_elements, loc=4)
                    # plt.axis('off')
                    # plt.savefig('%s/%s_hartley_OnOff_legend.svg' % (result_path, file_name), dpi=300, format='svg')


            ## Initialize plot for multiple planes plotting of sf type
            print('Processing Multiple planes')
            fig, ax = plt.subplots()

            for pl in range(np.size(planeId)):

                plane = planeId[pl]
                # load hue data
                data_file = os.path.join(planeData_path,'%s_%s_%s_sum.csv' %(animal, unit, plane))
                plda = pd.read_csv(data_file, index_col=0)
                visResp = plda.visResp
                xloc=plda.xloc-1
                yloc=plda.yloc-1
                ## load background images and ROIs
                # I use images from orisf_achromatic data as background image. All maps are plotted on this image.
                roibkg_file = os.path.join(planeData_path,'%s_%s_%s_roibkg.jld2' %(animal, unit, plane))
                roibkg = h5py.File(roibkg_file, 'r+')
                bkgimg = roibkg['bkg'][()]
                whitBK = np.ones(np.transpose(bkgimg).shape)*0.8

                # load roi segements/contours
                rois = roibkg["roi"][()]

                # Transform roi contours to patches
                allROIs = []   # patches of all ROIs
                for i in range(np.size(rois)):
                    allROIs.append(0)    # append 0 if there is ROI
                    #Find the segment/contour for this cell
                    roibkg[rois[i]][()] = roibkg[rois[i]][()].astype(int)
                    allROIs[i] = Polygon([tuple(l) for l in list(np.transpose(roibkg[rois[i]][()]-1))])

                if white_BK:
                    ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                else:
                    ax.imshow(bkgimg, cmap='gray')

                for vi in np.arange(numCell):
                    if plId[vi]==int(plane):
                        if visResp[cellId[vi]-1]>0:
                            if cs[vi]>0:  # coen significant

                                # ax.add_patch(
                                #     PolygonPatch(allROIs[cellId[vi] - 1],
                                #                  alpha=1,
                                #                  color='black'))
                                ax.scatter(xloc[cellId[vi] - 1],
                                           yloc[cellId[vi] - 1],
                                           s=5,
                                           color='gray',
                                           marker=sfTypeCmap[type_sf[vi]])


            ax.set_rasterized(True)
            ax.set_xticklabels([])
            ax.set_yticklabels([])
            ax.minorticks_off()
            ax.set_frame_on(False)
            plt.grid(True)
            plt.grid(color='gray', linestyle='--', linewidth=0.5, alpha=0.3)

            # plt.axis('off')
            plt.savefig('%s/%s_thres%s_hartley_SFtype.svg' % (result_path_multiplane, file_name_allplane,thres), dpi=300, bbox_inches='tight', pad_inches=0, format='svg')
            plt.savefig('%s/%s_thres%s_hartley_SFtype.png' % (result_path_multiplane, file_name_allplane,thres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
            # plt.show()

            # fig, ax = plt.subplots()
            # ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
            # legend_elements = [Patch(facecolor='b', edgecolor='b', label=stim+' OFF'),
            #                 Patch(facecolor='r', edgecolor='r', label=stim+' ON')]
            # ax.legend(handles=legend_elements, loc=4)
            # plt.axis('off')
            # plt.savefig('%s/%s_hartley_OnOff_legend.svg' % (result_path, file_name), dpi=300, format='svg')

            ## Initialize plot for multiple planes plotting of sf passwidth
            print('Processing Multiple planes')
            fig, ax = plt.subplots()

            for pl in range(np.size(planeId)):

                plane = planeId[pl]
                # load hue data
                data_file = os.path.join(
                    planeData_path, '%s_%s_%s_sum.csv' % (animal, unit, plane))
                plda = pd.read_csv(data_file, index_col=0)
                visResp = plda.visResp
                xloc = plda.xloc - 1
                yloc = plda.yloc - 1
                ## load background images and ROIs
                # I use images from orisf_achromatic data as background image. All maps are plotted on this image.
                roibkg_file = os.path.join(
                    planeData_path,
                    '%s_%s_%s_roibkg.jld2' % (animal, unit, plane))
                roibkg = h5py.File(roibkg_file, 'r+')
                bkgimg = roibkg['bkg'][()]
                whitBK = np.ones(np.transpose(bkgimg).shape) * 0.8

                # load roi segements/contours
                rois = roibkg["roi"][()]

                # Transform roi contours to patches
                allROIs = []  # patches of all ROIs
                for i in range(np.size(rois)):
                    allROIs.append(0)  # append 0 if there is ROI
                    #Find the segment/contour for this cell
                    roibkg[rois[i]][()] = roibkg[rois[i]][()].astype(int)
                    allROIs[i] = Polygon([tuple(l) for l in list(np.transpose(roibkg[rois[i]][()] - 1))])

                if white_BK:
                    ax.imshow(whitBK,
                              cmap='gray',
                              alpha=BK_alpha,
                              vmin=0,
                              vmax=1)
                else:
                    ax.imshow(bkgimg, cmap='gray')

                for vi in np.arange(numCell):
                    if plId[vi] == int(plane):
                        if visResp[cellId[vi] - 1] > 0:
                            ax.add_patch(
                                PolygonPatch(allROIs[cellId[vi] - 1],
                                             alpha=1,
                                             color=gray))
                            if cs[vi] > 0:  # cone significant
                                ax.add_patch(
                                    PolygonPatch(allROIs[cellId[vi] - 1],
                                                 alpha=psf[vi]/np.max(psf) * 0.7 + 0.3,
                                                 color=cmap_patch_pw(sfpw[vi] / np.max(sfpw))))

            ax.set_rasterized(True)
            ax.set_xticklabels([])
            ax.set_yticklabels([])
            ax.minorticks_off()
            ax.set_frame_on(False)
            plt.grid(True)
            plt.grid(color='gray', linestyle='--', linewidth=0.5, alpha=0.3)

            # plt.axis('off')
            plt.savefig('%s/%s_thres%s_hartley_SFpw.svg' %
                        (result_path_multiplane, file_name_allplane, thres),
                        dpi=300,
                        bbox_inches='tight',
                        pad_inches=0,
                        format='svg')
            plt.savefig('%s/%s_thres%s_hartley_SFpw.png' %
                        (result_path_multiplane, file_name_allplane, thres),
                        dpi=300,
                        bbox_inches='tight',
                        pad_inches=0,
                        format='png')
            # plt.show()

        ## Initialize plot for multiple planes plotting of cell type
        print('Processing Multiple planes')
        fig, ax = plt.subplots()
        file_name_allplane = os.path.join('%s_u%s_thres%s_allplane' %
                                          (animal, unit, thres))
        for pl in range(np.size(planeId)):

            plane = planeId[pl]
            # load hue data
            data_file = os.path.join(
                planeData_path, '%s_%s_%s_sum.csv' % (animal, unit, plane))
            plda = pd.read_csv(data_file, index_col=0)
            visResp = plda.visResp
            cellId = plda.cellId.values
            numCell = np.size(cellId)
            xloc = plda.xloc - 1
            yloc = plda.yloc - 1

            # ## Load cone-isolating drifting data
            # expt_path = os.path.join(unit_path,
            #                          '%s_%s_%s' % (unit, exptId, plane),
            #                          'Plots')
            # fdata_file = os.path.join(
            #     expt_path,
            #     '%s_%s_%s_%s_result.csv' % (animal, unit, exptId, plane))

            # fdata = pd.read_csv(fdata_file, index_col=0)

            # visResp0 = fdata.visResp

            # visResp = visResp0 | visResp1
            sftplda = np.reshape(result[plane],
                                 (int(np.size(result[plane]) / 4), 4),
                                 order='F')

            ## Load cone-isolating STA data
            sta_file = os.path.join(
                planeData_path,
                '%s_%s_thres%s_sta_dataset.csv' % (animal, unit, thres))
            sta = pd.read_csv(sta_file, index_col=0)

            iscone = sta.iscone.values
            isl = sta.isl.values
            ism = sta.ism.values
            iss = sta.iss.values
            isa = sta.isa.values

            lsign = sta.lsign.values
            msign = sta.msign.values
            ssign = sta.ssign.values
            asign = sta.asign.values

            ## load background images and ROIs
            # I use images from orisf_achromatic data as background image. All maps are plotted on this image.
            roibkg_file = os.path.join(
                planeData_path, '%s_%s_%s_roibkg.jld2' % (animal, unit, plane))
            roibkg = h5py.File(roibkg_file, 'r+')
            bkgimg = roibkg['bkg'][()]
            whitBK = np.ones(np.transpose(bkgimg).shape) * 0.8

            # load roi segements/contours
            rois = roibkg["roi"][()]

            # Transform roi contours to patches
            allROIs = []  # patches of all ROIs
            for i in range(np.size(rois)):
                allROIs.append(0)  # append 0 if there is ROI
                #Find the segment/contour for this cell
                roibkg[rois[i]][()] = roibkg[rois[i]][()].astype(int)
                allROIs[i] = Polygon([tuple(l) for l in list(np.transpose(roibkg[rois[i]][()] - 1))])

            if white_BK:
                ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
            else:
                ax.imshow(bkgimg, cmap='gray')

            for vi in np.arange(numCell):

                if visResp[cellId[vi] - 1] > 0:
                    if ((sftplda[vi, 0] == 'L') | (sftplda[vi, 1] == 'L') |
                        (sftplda[vi, 2] == 'L')) & (
                            (sftplda[vi, 3] == 'B') |
                            (sftplda[vi, 3] == 'H')):  # type I

                        ax.add_patch(
                            PolygonPatch(allROIs[cellId[vi] - 1],
                                         alpha=1,
                                         color='red'))
                        # ax.scatter(xloc[cellId[vi] - 1],
                        #             yloc[cellId[vi] - 1],
                        #             s=5,
                        #             color='red',
                        #             marker=cellTypeCmap['I'])
                        ax.text(xloc[cellId[vi] - 1],
                                yloc[cellId[vi] - 1],
                                str(cellId[vi]),
                                horizontalalignment='center',
                                fontsize=3)  # add cellId
                    if ((sftplda[vi, 0] == 'L') | (sftplda[vi, 1] == 'L') |
                        (sftplda[vi, 2] == 'L')) & (sftplda[vi, 3]
                                                    == 'L'):  # Type II
                        ax.add_patch(
                            PolygonPatch(allROIs[cellId[vi] - 1],
                                         alpha=1,
                                         color='green'))
                        # ax.scatter(xloc[cellId[vi] - 1],
                        #             yloc[cellId[vi] - 1],
                        #             s=5,
                        #             color='green',
                        #             marker=cellTypeCmap['II'])
                        ax.text(xloc[cellId[vi] - 1],
                                yloc[cellId[vi] - 1],
                                str(cellId[vi]),
                                horizontalalignment='center',
                                fontsize=3)  # add cellId
                    if ((sftplda[vi, 0] == 'NaN') & (sftplda[vi, 1] == 'NaN') &
                        (sftplda[vi, 2] == 'NaN')) & ((sftplda[vi, 3] == 'L') | (sftplda[vi, 3] == 'B') |
                        (sftplda[vi, 3] == 'H')):  # type III

                        ax.add_patch(
                            PolygonPatch(allROIs[cellId[vi] - 1],
                                         alpha=1,
                                         color='gray'))
                        # ax.scatter(xloc[cellId[vi] - 1],
                        #             yloc[cellId[vi] - 1],
                        #             s=5,
                        #             color='blue',
                        #             marker=cellTypeCmap['III'])
                        ax.text(xloc[cellId[vi] - 1],
                                yloc[cellId[vi] - 1],
                                str(cellId[vi]),
                                horizontalalignment='center',
                                fontsize=3)  # add cellId

                    if ((sftplda[vi, 0] == 'B') | (sftplda[vi, 0] == 'H') |
                        (sftplda[vi, 1] == 'B') | (sftplda[vi, 1] == 'H') |
                        (sftplda[vi, 2] == 'B') | (sftplda[vi, 2] == 'H')) & (
                            (sftplda[vi, 3] == 'B') |
                            (sftplda[vi, 3] == 'H')):  # Double-opponent

                        ax.add_patch(
                            PolygonPatch(allROIs[cellId[vi] - 1],
                                         alpha=1,
                                         color='blue'))
                        # ax.scatter(xloc[cellId[vi] - 1],
                        #             yloc[cellId[vi] - 1],
                        #             s=5,
                        #             color='blue',
                        #             marker=cellTypeCmap['DO'])
                        ax.text(xloc[cellId[vi] - 1],
                                yloc[cellId[vi] - 1],
                                str(cellId[vi]),
                                horizontalalignment='center',
                                fontsize=3)  # add cellId

        ax.set_rasterized(True)
        ax.set_xticklabels([])
        ax.set_yticklabels([])
        ax.minorticks_off()
        ax.set_frame_on(False)
        plt.grid(True)
        plt.grid(color='gray', linestyle='--', linewidth=0.5, alpha=0.3)

        # plt.axis('off')
        plt.savefig('%s/%s_hartley_celltype.svg' %
                    (result_path_multiplane, file_name_allplane),
                    dpi=300,
                    bbox_inches='tight',
                    pad_inches=0,
                    format='svg')
        plt.savefig('%s/%s_hartley_celltype.png' %
                    (result_path_multiplane, file_name_allplane),
                    dpi=300,
                    bbox_inches='tight',
                    pad_inches=0,
                    format='png')
        # plt.show()



if plot_cellType_Drift:

    for j, unit in enumerate(units):
        print('Processing Cone Drifting Unit %s.' % unit)

        unit_path = os.path.join(main_path, 'U%s' % unit)
        planeData_path = os.path.join(
            unit_path, '_Summary',
            'DataExport')  # has data organized in plane dimension
        result_path_plane = os.path.join(unit_path, '_Summary', 'Multiplane')
        result_path_multiplane = os.path.join(result_path_plane,
                                              '0. Original maps')
        result = {}
        for pl in range(np.size(planeId)):
            result[planeId[pl]] = np.array([])

        # Make folders for saving data from single unit
        if not os.path.exists(result_path_plane):
            os.mkdir(result_path_plane)
        if not os.path.exists(result_path_multiplane):
            os.mkdir(result_path_multiplane)

        for i, (exptId, stim) in enumerate(expt_cone_drift.items()):
            print('Processing %s stimulus.' % stim)
            file_name_allplane = os.path.join('%s_u%s_%s_thres%s_allplane' %
                                          (animal, unit, stim, thres))

            if singleplane:

                for pl in range(np.size(planeId)):

                    plane = planeId[pl]

                    ## Load cone significance data
                    expt_path = os.path.join(unit_path,
                                                '%s_%s_%s' % (unit, exptId, plane),
                                                'Plots')

                    # load cone drifting data
                    fdata_file = os.path.join(
                        expt_path,
                        '%s_%s_%s_%s_result.csv' % (animal, unit, exptId, plane))

                    fdata = pd.read_csv(fdata_file, index_col=0)

                    cellId = fdata.cellId.values

                    visResp = fdata.visResp

                    ori_auc = fdata.oriauc
                    dir_auc = fdata.dirauc

                    ori_cv = fdata.oricv  # cv of orientation  (1-mag)
                    dir_cv = fdata.dircv  # cv of direction (1-mag)

                    hw_ori = fdata.ohw
                    hw_dir = fdata.dhw

                    cv_ori = fdata.cvori  # preferred orientation from vector summation/cv
                    cv_dir = fdata.cvdir # preferred direction from vector summation/cv
                    fit_ori = fdata.fitori  # preferred orientation from cv
                    fit_dir = fdata.fitdir
                    fit_osi = fdata.osi1
                    fit_dsi = fdata.dsi1

                    type_sf = fdata.sftype
                    hw_sf = fdata.sfhw
                    pw_sf = fdata.sfpw
                    fit_sf = fdata.fitsf
                    max_sf = fdata.maxsf

                    numCell = np.size(cellId)

                    # result[plane] = np.hstack((result[plane], type_sf))

                    # load hue data for visResp checking
                    data_file = os.path.join(
                        planeData_path,
                        '%s_%s_%s_sum.csv' % (animal, unit, plane))
                    # data_file = os.path.join(planeData_path, '%s_%s_%s_oriData.csv' % (animal, unit, plane))
                    plda = pd.read_csv(data_file, index_col=0)
                    # visResp1 = plda.visResp
                    xloc = plda.xloc - 1
                    yloc = plda.yloc - 1
                    # visResp = visResp0 | visResp1
                    print('Processing plane: ' + plane)

                    # for saving results
                    result_path_singleplane = os.path.join(
                        unit_path, '_Summary', 'plane_%s' % (plane),
                        '0. Original maps')
                    result_path_unit = os.path.join(unit_path, '%s_%s_%s' % (unit, exptId, plane),'Plots') # only has ori/dir data
                    file_name_singleplane = os.path.join(
                        '%s_u%s_plane%s_%s_singleplane' %
                        (animal, unit, plane, stim))
                    file_name_ori = os.path.join(
                        '%s_u%s_%s_plane%s_%s_ori' %
                        (animal, unit, exptId, plane, stim))
                    file_name_sf = os.path.join(
                        '%s_u%s_%s_plane%s_%s_sf' %
                        (animal, unit, exptId, plane, stim))
                    file_name_dir = os.path.join(
                        '%s_u%s_%s_plane%s_%s_dir' %
                        (animal, unit, exptId, plane, stim))

                    ## load background images and ROIs
                    # I use images from orisf_achromatic data as background image. All maps are plotted on this image.
                    roibkg_file = os.path.join(
                        planeData_path,
                        '%s_%s_%s_roibkg.jld2' % (animal, unit, plane))
                    # roibkg_file = os.path.join(planeData_path, 'AE6_002_011_roibkg.jld2')   # Sometimes, there was z-shift between Hartley and ori/hue, so the ROIs are different.
                    roibkg = h5py.File(roibkg_file, 'r+')
                    bkgimg = np.transpose(roibkg['bkg'][()])
                    whitBK = np.ones(bkgimg.shape) * 0.8

                    # load roi segements/contours
                    rois = roibkg["roi"][()]

                    # Transform roi contours to patches
                    allROIs = []  # patches of all ROIs
                    for i in range(np.size(rois)):
                        allROIs.append(0)  # append 0 if there is ROI
                        #Find the segment/contour for this cell
                        roibkg[rois[i]][()] = roibkg[rois[i]][()].astype(int)
                        allROIs[i] = Polygon([tuple(l) for l in list(np.transpose(roibkg[rois[i]][()] - 1))])


                    # plot orientation map using fitting parameters
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')

                    for vi in np.arange(numCell):
                        if visResp[vi]>0:
                            ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
                        if (ori_auc[vi]>oriaucThres) & (visResp[vi]>0):
                            if np.isnan(fit_ori[vi]):
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=gray))
                            else:
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1 , color=ori_lut(fit_ori[vi]/180)))  #alpha=np.log10(ori_p[vi])/np.min(np.log10(ori_p)) alpha=1-(ori_p[vi]-np.min(ori_p[:]))/(np.max(ori_p[:])-np.min(ori_p[:])),
                    ax.set_rasterized(True)
                    plt.axis('off')
                    # plt.savefig('%s/%s_auc%s_fit.svg' % (result_path_unit,  file_name_ori), facecolor='#ffffff', dpi=300, format='svg')
                    plt.savefig('%s/%s_auc%s_fit.png' % (result_path_singleplane, file_name_ori,oriaucThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    plt.savefig('%s/%s_auc%s_fit.png' % (result_path_unit, file_name_ori,oriaucThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')

                    # plot orientation map using cv parameters
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')

                    for vi in np.arange(numCell):
                        if visResp[vi]>0:
                            ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
                        if (ori_auc[vi]>oriaucThres) & (visResp[vi]>0):
                            ax.add_patch(PolygonPatch(allROIs[vi], alpha=1 , color=ori_lut(cv_ori[vi]/180)))  #alpha=np.log10(ori_p[vi])/np.min(np.log10(ori_p)) alpha=1-(ori_p[vi]-np.min(ori_p[:]))/(np.max(ori_p[:])-np.min(ori_p[:])),
                    ax.set_rasterized(True)
                    plt.axis('off')
                    # plt.savefig('%s/%s_auc%s_cv.svg' % (result_path_unit,  file_name_ori), facecolor='#ffffff', dpi=300, format='svg')
                    plt.savefig('%s/%s_auc%s_cv.png' % (result_path_singleplane, file_name_ori,oriaucThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    plt.savefig('%s/%s_auc%s_cv.png' % (result_path_unit, file_name_ori,oriaucThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')


                    # plot OSI map, osi is calculated after fitting
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')
                    for vi in np.arange(numCell):
                        if visResp[vi]>0:
                            ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
                        if (ori_auc[vi] > oriaucThres) & (visResp[vi]>0):
                            if np.isnan(fit_osi[vi]):
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=gray))
                            else:
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=cmap_patch_osi(fit_osi[vi])))
                    # ax.set_title( file_name_ori, fontsize=font_size)
                    ax.set_rasterized(True)
                    plt.axis('off')
                    # plt.savefig('%s/%s_osi_fit.svg' % (result_path_unit,  file_name_ori), facecolor='#ffffff', dpi=300, format='svg')
                    plt.savefig('%s/%s_osi_fit.png' % (result_path_singleplane, file_name_ori), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    plt.savefig('%s/%s_osi_fit.png' % (result_path_unit, file_name_ori), dpi=300, bbox_inches='tight', pad_inches=0, format='png')


                    # plot Circular Variance map
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')
                    for vi in np.arange(numCell):
                        if visResp[vi] > 0:
                            ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=gray))
                        if (ori_auc[vi] > oriaucThres) & (visResp[vi] > 0):
                            if np.isnan(ori_cv[vi]):
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=gray))
                            else:
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=cmap_patch_osi(ori_cv[vi])))
                    # ax.set_title( file_name_ori, fontsize=font_size)
                    ax.set_rasterized(True)
                    plt.axis('off')
                    # plt.savefig('%s/%s_cv_fit.svg' % (result_path_unit,  file_name_ori), facecolor='#ffffff', dpi=300, format='svg')
                    plt.savefig('%s/%s_cv_fit.png' % (result_path_singleplane, file_name_ori), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    plt.savefig('%s/%s_cv_fit.png' % (result_path_unit, file_name_ori), dpi=300, bbox_inches='tight', pad_inches=0, format='png')

                    # plot Ori half width map
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')
                    for vi in np.arange(numCell):
                        if visResp[vi] > 0:
                            ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=gray))
                        if (ori_auc[vi] > oriaucThres) & (visResp[vi] > 0):
                            if type(hw_ori[vi]) != str:
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=gray))
                            else:
                                hwori = float(re.findall(r"[-+]?\d*\.\d+|\d+", hw_ori[vi])[0])
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=cmap_patch_osi(hwori/60)))
                    # ax.set_title( file_name_ori, fontsize=font_size)
                    ax.set_rasterized(True)
                    plt.axis('off')
                    # plt.savefig('%s/%s_cv_fit.svg' % (result_path_unit,  file_name_ori), facecolor='#ffffff', dpi=300, format='svg')
                    plt.savefig('%s/%s_hw_fit.png' % (result_path_singleplane, file_name_ori), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    plt.savefig('%s/%s_hw_fit.png' % (result_path_unit, file_name_ori), dpi=300, bbox_inches='tight', pad_inches=0, format='png')

                    # Plot spatial frequency preference map using fitting parameters
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')

                    for vi in np.arange(numCell):
                        if visResp[vi]>0:
                            if np.isnan(fit_sf[vi]):
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
                            else:
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=cmap_patch_sf(fit_sf[vi] / np.max(fit_sf))))
                    ax.set_rasterized(True)
                    # ax.set_title('Spatial Frequency Tuning Map', fontsize=font_size)
                    plt.axis('off')
                    # plt.savefig('%s/%s_sf.svg' % (result_path_unit, file_name_sf), dpi=300, format='svg')
                    plt.savefig('%s/%s_fit.png' % (result_path_singleplane, file_name_sf), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    plt.savefig('%s/%s_fit.png' % (result_path_unit, file_name_sf), dpi=300, bbox_inches='tight', pad_inches=0, format='png')

                    # Plot spatial frequency passwidth map using fitting parameters
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')

                    for vi in np.arange(numCell):
                        if visResp[vi]>0:
                            if np.isnan(pw_sf[vi]):
                                ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
                            else:
                                # hwsf = float(re.findall(r"[-+]?\d*\.\d+|\d+", hw_sf[vi])[0])
                                ax.add_patch(
                                    PolygonPatch(
                                        allROIs[vi],
                                        alpha=fit_sf[vi] / np.max(fit_sf) * 0.7 + 0.3,
                                        color=cmap_patch_pw(pw_sf[vi] / np.max(pw_sf))))

                    ax.set_rasterized(True)
                    # ax.set_title('Spatial Frequency Pass width Map', fontsize=font_size)
                    plt.axis('off')
                    # plt.savefig('%s/%s_pw_fit.svg' % (result_path_unit, file_name_sf), dpi=300, format='svg')
                    plt.savefig('%s/%s_pw_fit.png' % (result_path_singleplane, file_name_sf), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    plt.savefig('%s/%s_pw_fit.png' % (result_path_unit, file_name_sf), dpi=300, bbox_inches='tight', pad_inches=0, format='png')

                    # Plot spatial frequency type map using fitting parameters
                    fig, ax = plt.subplots()
                    if white_BK:
                        ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    else:
                        ax.imshow(bkgimg, cmap='gray')

                    for vi in np.arange(numCell):
                        if visResp[vi]>0:
                            if type(type_sf[vi]) == str:
                                ax.scatter(xloc[cellId[vi] - 1],
                                        yloc[cellId[vi] - 1],
                                        s=5,
                                        color='gray',
                                        marker=sfTypeCmap[type_sf[vi]])
                                result[plane] = np.hstack((result[plane], type_sf[vi]))
                            else:
                                result[plane] = np.hstack((result[plane], 'NaN'))
                        else:
                            result[plane] = np.hstack((result[plane], 'NaN'))
                    ax.set_rasterized(True)
                    # ax.set_title('Spatial Frequency Tuning Map', fontsize=font_size)
                    plt.axis('off')
                    # plt.savefig('%s/%s_sf.svg' % (result_path_unit, file_name_sf), dpi=300, format='svg')
                    plt.savefig('%s/%s_sfType_fit.png' % (result_path_singleplane, file_name_sf), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    plt.savefig('%s/%s_sfType_fit.png' % (result_path_unit, file_name_sf), dpi=300, bbox_inches='tight', pad_inches=0, format='png')

                    # # plot Direction map using fitting parameters
                    # fig, ax = plt.subplots()
                    # if white_BK:
                    #     ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    #     # file_name_dir = os.path.join('%s_whtBK%s' % (file_name_dir, str(white_BK)))
                    # else:
                    #     ax.imshow(bkgimg, cmap='gray')

                    # for vi in np.arange(numCell):
                    #     if visResp[vi]>0:
                    #         ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
                    #     if (dir_auc[vi]>diraucThres) & (visResp[vi]>0):
                    #         if np.isnan(fit_dir[vi]):
                    #             ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=gray))
                    #         else:
                    #             ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=ori_lut(fit_dir[vi]/360)))  # direction
                    #             # ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=ori_lut(np.mod(fit_dir[vi]-90,180)/180)))  # orientation inferred from direction
                    # ax.set_rasterized(True)
                    # plt.axis('off')
                    # # plt.savefig('%s/%s_auc%s_fit.svg' % (result_path_unit,  file_name_dir), facecolor='#ffffff', dpi=300, format='svg')
                    # plt.savefig('%s/%s_auc%s_fit.png' % (result_path_singleplane, file_name_dir,diraucThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    # plt.savefig('%s/%s_auc%s_fit.png' % (result_path_unit, file_name_dir,diraucThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')


                    # # plot Direction map using cv parameters
                    # fig, ax = plt.subplots()
                    # if white_BK:
                    #     ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    #     # file_name_dir = os.path.join('%s_whtBK%s' % (file_name_dir, str(white_BK)))
                    # else:
                    #     ax.imshow(bkgimg, cmap='gray')

                    # for vi in np.arange(numCell):
                    #     if visResp[vi]>0:
                    #         ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
                    #     if (dir_auc[vi]>diraucThres) & (visResp[vi]>0):
                    #         ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=ori_lut(cv_dir[vi]/360)))  # direction
                    #         # ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=ori_lut(np.mod(cv_dir[vi]-90,180)/180)))  # orientation inferred from direction
                    # ax.set_rasterized(True)
                    # plt.axis('off')
                    # # plt.savefig('%s/%s_auc%s_cv.svg' % (result_path_unit,  file_name_dir), facecolor='#ffffff', dpi=300, format='svg')
                    # plt.savefig('%s/%s_auc%s_cv.png' % (result_path_singleplane, file_name_dir,diraucThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    # plt.savefig('%s/%s_auc%s_cv.png' % (result_path_unit, file_name_dir,diraucThres), dpi=300, bbox_inches='tight', pad_inches=0, format='png')

                    # # plot Direction hw map
                    # fig, ax = plt.subplots()
                    # if white_BK:
                    #     ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
                    #     # file_name_dir = os.path.join('%s_whtBK%s' % (file_name_dir, str(white_BK)))
                    # else:
                    #     ax.imshow(bkgimg, cmap='gray')

                    # for vi in np.arange(numCell):
                    #     if visResp[vi]>0:
                    #         if type(hw_ori[vi]) != str:
                    #             ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color= gray))
                    #         else:
                    #             hwdir = float(re.findall(r"[-+]?\d*\.\d+|\d+", hw_dir[vi])[0])
                    #             ax.add_patch(PolygonPatch(allROIs[vi], alpha=1, color=cmap_patch_sf(hwdir/180)))
                    # ax.set_rasterized(True)
                    # plt.axis('off')
                    # # plt.savefig('%s/%s_auc%s_cv.svg' % (result_path_unit,  file_name_dir), facecolor='#ffffff', dpi=300, format='svg')
                    # plt.savefig('%s/%s_hw.png' % (result_path_singleplane, file_name_dir), dpi=300, bbox_inches='tight', pad_inches=0, format='png')
                    # plt.savefig('%s/%s_hw.png' % (result_path_unit, file_name_dir), dpi=300, bbox_inches='tight', pad_inches=0, format='png')


        ## Initialize plot for multiple planes plotting of cell type
        print('Processing Multiple planes')
        fig, ax = plt.subplots()
        file_name_allplane = os.path.join('%s_u%s_thres%s_allplane' %
                            (animal, unit, thres))
        for pl in range(np.size(planeId)):

            plane = planeId[pl]

            # load hue data
            data_file = os.path.join(
                planeData_path, '%s_%s_%s_sum.csv' % (animal, unit, plane))
            plda = pd.read_csv(data_file, index_col=0)
            visResp = plda.visResp
            cellId = plda.cellId.values
            numCell = np.size(cellId)
            xloc = plda.xloc - 1
            yloc = plda.yloc - 1

            ## Load cone-isolating drifting data
            # expt_path = os.path.join(unit_path,
            #                             '%s_%s_%s' % (unit, exptId, plane),
            #                             'Plots')
            # fdata_file = os.path.join(
            #     expt_path,
            #     '%s_%s_%s_%s_result.csv' % (animal, unit, exptId, plane))

            # fdata = pd.read_csv(fdata_file, index_col=0)

            # visResp0 = fdata.visResp

            # visResp = visResp0 | visResp1
            sftplda = np.reshape(result[plane],(int(np.size(result[plane])/4), 4),order='F')

            ## Load cone-isolating STA data
            sta_file = os.path.join(planeData_path, '%s_%s_thres%s_sta_dataset.csv' % (animal, unit, thres))
            sta = pd.read_csv(sta_file, index_col=0)

            iscone = sta.iscone.values
            isl = sta.isl.values
            ism = sta.ism.values
            iss = sta.iss.values
            isa = sta.isa.values


            lsign = sta.lsign.values
            msign = sta.msign.values
            ssign = sta.ssign.values
            asign = sta.asign.values

            ## load background images and ROIs
            # I use images from orisf_achromatic data as background image. All maps are plotted on this image.
            roibkg_file = os.path.join(
                planeData_path,
                '%s_%s_%s_roibkg.jld2' % (animal, unit, plane))
            roibkg = h5py.File(roibkg_file, 'r+')
            bkgimg = roibkg['bkg'][()]
            whitBK = np.ones(np.transpose(bkgimg).shape) * 0.8

            # load roi segements/contours
            rois = roibkg["roi"][()]

            # Transform roi contours to patches
            allROIs = []  # patches of all ROIs
            for i in range(np.size(rois)):
                allROIs.append(0)  # append 0 if there is ROI
                #Find the segment/contour for this cell
                roibkg[rois[i]][()] = roibkg[rois[i]][()].astype(int)
                allROIs[i] = Polygon([tuple(l) for l in list(np.transpose(roibkg[rois[i]][()] - 1))])

            if white_BK:
                ax.imshow(whitBK,
                            cmap='gray',
                            alpha=BK_alpha,
                            vmin=0,
                            vmax=1)
            else:
                ax.imshow(bkgimg, cmap='gray')

            for vi in np.arange(numCell):

                if visResp[cellId[vi] - 1] > 0:
                    if ((sftplda[vi, 0] == 'L') | (sftplda[vi, 1] == 'L') | (sftplda[vi, 2] == 'L')) & ((sftplda[vi,3] == 'B') | (sftplda[vi,3] == 'H')):  # type I

                        ax.add_patch(
                            PolygonPatch(allROIs[cellId[vi] - 1],
                                         alpha=1,
                                         color='red'))
                        # ax.scatter(xloc[cellId[vi] - 1],
                        #             yloc[cellId[vi] - 1],
                        #             s=5,
                        #             color='red',
                        #             marker=cellTypeCmap['I'])
                        ax.text(xloc[cellId[vi]-1],yloc[cellId[vi]-1], str(cellId[vi]),horizontalalignment='center', fontsize=3)   # add cellId
                    if ((sftplda[vi, 0] == 'L') | (sftplda[vi, 1] == 'L') | (sftplda[vi, 2] == 'L')) & (sftplda[vi,3] == 'L'):    # Type II
                        ax.add_patch(
                            PolygonPatch(allROIs[cellId[vi] - 1],
                                         alpha=1,
                                         color='green'))
                        # ax.scatter(xloc[cellId[vi] - 1],
                        #             yloc[cellId[vi] - 1],
                        #             s=5,
                        #             color='green',
                        #             marker=cellTypeCmap['II'])
                        ax.text(xloc[cellId[vi]-1],yloc[cellId[vi]-1], str(cellId[vi]),horizontalalignment='center', fontsize=3)   # add cellId

                    if ((sftplda[vi, 0] == 'NaN') & (sftplda[vi, 1] == 'NaN') &
                        (sftplda[vi, 2] == 'NaN')) & ((sftplda[vi, 3] == 'L') | (sftplda[vi, 3] == 'B') |
                        (sftplda[vi, 3] == 'H')):  # type III

                        ax.add_patch(
                            PolygonPatch(allROIs[cellId[vi] - 1],
                                         alpha=1,
                                         color='gray'))
                        # ax.scatter(xloc[cellId[vi] - 1],
                        #             yloc[cellId[vi] - 1],
                        #             s=5,
                        #             color='blue',
                        #             marker=cellTypeCmap['III'])
                        ax.text(xloc[cellId[vi] - 1],
                                yloc[cellId[vi] - 1],
                                str(cellId[vi]),
                                horizontalalignment='center',
                                fontsize=3)  # add cellId
                    if ((sftplda[vi, 0] == 'B') | (sftplda[vi, 0] == 'H') | (sftplda[vi, 1] == 'B') | (sftplda[vi, 1] == 'H') | (sftplda[vi, 2] == 'B') | (sftplda[vi, 2] == 'H')) & ((sftplda[vi,3] == 'B') | (sftplda[vi,3] == 'H')):  # Double-opponent

                        ax.add_patch(
                            PolygonPatch(allROIs[cellId[vi] - 1],
                                         alpha=1,
                                         color='blue'))
                        # ax.scatter(xloc[cellId[vi] - 1],
                        #             yloc[cellId[vi] - 1],
                        #             s=5,
                        #             color='blue',
                        #             marker=cellTypeCmap['III'])
                        ax.text(xloc[cellId[vi]-1],yloc[cellId[vi]-1], str(cellId[vi]),horizontalalignment='center', fontsize=3)   # add cellId


        ax.set_rasterized(True)
        ax.set_xticklabels([])
        ax.set_yticklabels([])
        ax.minorticks_off()
        ax.set_frame_on(False)
        plt.grid(True)
        plt.grid(color='gray', linestyle='--', linewidth=0.5, alpha=0.3)

        # plt.axis('off')
        plt.savefig('%s/%s_coneDrift_celltype.svg' %
                    (result_path_multiplane, file_name_allplane),
                    dpi=300,
                    bbox_inches='tight',
                    pad_inches=0,
                    format='svg')
        plt.savefig('%s/%s_coneDrift_celltype.png' %
                    (result_path_multiplane, file_name_allplane),
                    dpi=300,
                    bbox_inches='tight',
                    pad_inches=0,
                    format='png')
        # plt.show()

        # fig, ax = plt.subplots()
        # ax.imshow(whitBK, cmap='gray', alpha=BK_alpha, vmin=0, vmax=1)
        # legend_elements = [Patch(facecolor='b', edgecolor='b', label=stim+' OFF'),
        #                 Patch(facecolor='r', edgecolor='r', label=stim+' ON')]
        # ax.legend(handles=legend_elements, loc=4)
        # plt.axis('off')
        # plt.savefig('%s/%s_hartley_OnOff_legend.svg' % (result_path, file_name), dpi=300, format='svg')
