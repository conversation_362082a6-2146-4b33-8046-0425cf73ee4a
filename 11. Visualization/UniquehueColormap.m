


% a=1:256;
% b=round(a./256*360);
% c=unique(b);
% 
% b=round((360-140)/360*256);


%% This unique hue color map is based on results from 'The cone inputs to the unique-hue mechanisms', 2005, VR

d1=linspace(1,0,92)';
e1=linspace(0, 0, 71)';
f1=linspace(0, 1, 53)';
g1=linspace(1,1,39)';

r=[d1(9:92);e1;f1;g1;d1(1:9)];

e2=linspace(0, 0, 83)';
f2=linspace(0, 1, 73)';
g2=linspace(1,1,51)';
d2=linspace(1,0,41)';
h2=linspace(0, 0, 8)';

g=[e2;f2;g2;d2;h2];

f3=linspace(0,1,92)';
d3=linspace(1,0,73)';
e3=linspace(0, 0, 91)';

% h3=linspace(0, 0, 8)';
b=[f3(9:91);d3;e3;f3(1:9)];

lut=horzcat(r,g,b);

