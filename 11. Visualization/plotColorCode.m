% Plot color code as a circle in DKL isolimunant plane.
% Peichao Li

double = 0;

% colorCode1 = {'#ffd6f9', '#ffd5ff', '#ffd7ff', '#dae2ff', '#8feeff', '#51f4ff', '#2bf5fa',...
%              '#40f6f1', '#77f3e5', '#c7e9dd', '#fedde4', '#ffd8ef'}; % Colors in DKL space
% colorCode2 = {'#FF8080', '#FF80BF', '#FF80FF', '#BF80FF', '#8080FF', '#80BFFF', '#80FFFF',...
%              '#80FFBF', '#80FF80', '#BFFF80', '#FFFF80', '#FFBF80'}; % HSV colors as keys for DKL colors

colorCode1 = colors([360,30:30:330],1:3);
colorCode2 = colors([360,30:30:330],1:3);

radius = 3;

axisThickness = 1;
labelFront = 40;

positionNum =  length(colorCode1); 
ang = [0:2*pi/positionNum:2*pi-0.001];


radius1 = radius * 2.5;
radius2 = radius * 4;

% position1X = radius1 * cos(ang)-radius/2;
% position1Y = radius1 * sin(ang)-radius/2;
position1X = radius1 * cos(ang);
position1Y = radius1 * sin(ang);
position2X = radius2 * cos(ang)-radius/2;
position2Y = radius2 * sin(ang)-radius/2;
sizeX = 2;
sizeY = 2;



% figure
% rectangle('Position',[0 0 2 4], 'FaceColor',[0 .5 .5],'Curvature',0.2)
if double
    for i = 1:length(colorCode1)
        pos1 = [position1X(i), position1Y(i), radius, radius]; 
%         colorCode = sscanf(colorCode1{i}(2:end),'%2x%2x%2x',[1 3])/255; % Hex to RGB, needed for Matlab 20108a
        colorCode = colorCode1(i,:);
        rectangle('Position',pos1,'Curvature',[1 1], 'FaceColor', colorCode);
        hold on;
        pos2 = [position2X(i), position2Y(i), radius, radius]; 
%         colorCode = sscanf(colorCode2{i}(2:end),'%2x%2x%2x',[1 3])/255;
        colorCode = colorCode2(i,:);
        rectangle('Position',pos2,'Curvature',[1 1], 'FaceColor', colorCode);
        hold on;
    end

    ax = gca;
    lim = radius2 + radius*2;
    xlim([-lim lim])
    ylim([-lim lim])
    xlabel('L-M', 'FontSize',labelFront, 'FontWeight','bold')
    ylabel('S-(L+M)', 'FontSize',labelFront, 'FontWeight','bold')
    set(ax,'xtick',[])
    set(ax,'xticklabel',[])
    set(ax,'ytick',[])
    set(ax,'yticklabel',[])
    set(ax,'linewidth',axisThickness)
    % ax.FontSize = tickFront;
    ax.XAxisLocation = 'origin';
    ax.YAxisLocation = 'origin';
    axis square
    hold off
end


r=1;
f = figure;
lim = radius2;
ax1 = axes('Parent',f);
xlim([-lim lim])
ylim([-lim lim])
% xlabel('L-M', 'FontSize',labelFront, 'FontWeight','bold')
% ylabel('S-(L+M)', 'FontSize',labelFront, 'FontWeight','bold')
set(ax1,'xtick',[])
set(ax1,'xticklabel',[])
set(ax1,'ytick',[])
set(ax1,'yticklabel',[])
set(ax1,'linewidth',axisThickness)
% ax.FontSize = tickFront;
ax1.XAxisLocation = 'origin';
ax1.YAxisLocation = 'origin';
axis square

for i = 1:length(colorCode1)
    
%     ax2 = axes('Parent',f);
    ax2 = axes('Position', [position1X(i)/50+0.468, position1Y(i)/30+0.4677, 0.1, 0.1]);
%     ax2 = axes('Position', [position1X(i)/50+0.467, position1Y(i)/26+0.4677, 0.1, 0.1]);
    
    
%     box off
%     semicrc_up = r.*[cos(0:0.1:pi); sin(0:0.1:pi)];
%     semicrc_down = r.*[cos(0:-0.1:-pi); sin(0:-0.1:-pi)];
    
    semicrc_up = r.*[cos(0:0.1:pi); sin(0:0.1:pi)];
    semicrc_down = r.*[cos(0:-0.1:-pi); sin(0:-0.1:-pi)];
    
%     colorCode = sscanf(colorCode1{i}(2:end),'%2x%2x%2x',[1 3])/255; % Hex to RGB, needed for Matlab 20108a
    colorCode = colorCode1(i,:);
    area(semicrc_up(1,:), semicrc_up(2,:), 'FaceColor',colorCode)
    hold on;
    
%     colorCode = sscanf(colorCode2{i}(2:end),'%2x%2x%2x',[1 3])/255;
    colorCode = colorCode2(i,:);
    
    
    area(semicrc_down(1,:), semicrc_down(2,:), 'FaceColor',colorCode)
%     set(ax2,'YDir','reverse'); % Flip image
    set(gca,'view',[0 90]);  % Horizontal view/image
%     set(gca,'view',[0 -90]);  % Vertical view/image
    axis square
    axis off
    hold on;
end
axis square

% ax2 = axes('Parent',f,'Units','normalized','Position',[.2 .2 .6 .3]);
% ax3 = axes('Parent',f);
% set(ax3,'Units','normalized','Position',[.5 .6 .3 .3]);




