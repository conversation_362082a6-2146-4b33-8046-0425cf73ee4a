
% Peichao: Plot color keys as ring

%% load color table
clc
clear
colorType = 'DKLmax'; % 'DKLmax', 'DKLmatch', 'HSL', 'Unique'
cmapFolder = 'C:\Users\<USER>\Dropbox\Github\2P\12. Visualization\ColorMap\';
cd(cmapFolder)

if strcmp(colorType,'DKLmax')
    load('cm_lidkl_mcchue_l0.mat')
elseif strcmp(colorType, 'DKLmatch')
    load('cm_dkl_mcchue_l0.mat')
elseif strcmp(colorType, 'HSL')
    load('cm_hsl_mshue_l0.4.mat')
elseif strcmp(colorType, 'Unique')
    load('uniquehue.mat')
elseif strcmp(colorType, 'Ori')
    load('ori_lut_alpha0.mat')
elseif strcmp(colorType, 'Dir')
    load('ori_lut_alpha0.mat')
end

resultFolder = strcat(cmapFolder, colorType,'\');
if ~isfolder(resultFolder)
    mkdir(resultFolder);    
end

imgName = strcat(colorType, '_ColorKey_Ring');

aa=-1000:1000;
[xx,yy] = meshgrid(aa,aa);
zz = xx+1i*yy;
% zz = abs(zz).*exp(1i*angle(zz)*2);
za=flip(round(rad2deg(angle(zz)))+180,2);
za(za==0)=360;
% za=round(za./360.*256);
% za=mod(za,180)+1;
% za=round(za./180.*256);

centerX = 0;
centerY = 0;
radius1 = 830;
radius2 = 730;
circlePixels1 = (yy - centerY).^2 + (xx - centerX).^2 <= radius1.^2;
circlePixels2 = (yy - centerY).^2 + (xx - centerX).^2 >= radius2.^2;
circlePixels =circlePixels1.*circlePixels2;
img=circlePixels.*za;
img(img==0)=361;
% img(img==0)=257;

% huelut=flip(hsv(256),1);
% lut=[huelut(:,1:3); [1,1,1]];

lut=colors(:,1:3);
lut(361,:) = [1 1 1];

% lut=lut(:,1:3);
% lut(257,:) = [1 1 1];

f = figure;
f.InnerPosition = [10 10 3000 3000];  % Define drawable region
colormap(lut)

imagesc(img)
% xline(1000, '-', 'LineWidth', 5, 'Color',[0 0 0], 'Alpha', 1)
% yline(1000, '-', 'LineWidth', 5, 'Color',[0 0 0], 'Alpha', 1)

ax = gca;
set(ax,'xtick',[])
set(ax,'xticklabel',[])
set(ax,'ytick',[])
set(ax,'yticklabel',[])
axis off
axis square

saveas(gcf, [resultFolder, imgName, '.svg'])
% fig2svg([resultFolder, imgName, '_new.svg'])

