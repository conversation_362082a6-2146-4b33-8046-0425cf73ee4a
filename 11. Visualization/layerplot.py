import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from shapely.geometry.polygon import Polygon
from descartes import PolygonPatch
import scipy.io as sio

color_hex = ['#af1600', '#8a4600', '#5a5d01', '#2a6600', '#006a00', '#006931', '#006464',
             '#0058b6', '#002DFF', '#6a2ade', '#97209b', '#aa1c50']
cmap_patch = plt.cm.get_cmap('jet')
cmap_patch_ori = plt.cm.get_cmap('hsv')
cmap_patch_osi = plt.cm.get_cmap('viridis')

animal = 'AE5'
units = ['006', '006']
trials_ach = ['003', '006']
trials_col = ['004', '007']
folder = '/Volumes/lacie/NHP data'
cell_plot_idx = 143
cell_plot = False

# Build DataFrame of all cells and segment files
all_pref_df = pd.DataFrame()
segment_dict = {}
for i, trial_ach in enumerate(trials_ach):
    all_pref_ach = pd.read_csv('%s/%s/%s_u%s_%s_ach_pref_df.csv' %
                               (folder, animal, animal, units[i], trial_ach), index_col=0)
    all_pref_col = pd.read_csv('%s/%s/%s_u%s_%s_color_pref_df.csv' %
                               (folder, animal, animal, units[i], trials_col[i]), index_col=0)

    match = pd.concat([all_pref_ach, all_pref_col], axis=1)
    match = match.loc[:, ~match.columns.duplicated()]

    segment_file = "%s/%s/2p_data/%s_%s_%s.segment" % (folder, animal, animal, units[i], trial_ach)
    segment = sio.loadmat(segment_file, squeeze_me=True, struct_as_record=False)['vert']
    segment_dict['u%s_%s' % (units[i], trial_ach)] = segment

    all_pref_df = pd.concat([all_pref_df, match])

fig, ax = plt.subplots()
fig_ca, ax_ca = plt.subplots()
fig_hue_osi, ax_hue_osi = plt.subplots()
fig_osi, ax_osi = plt.subplots()
osi_alpha = False
ca_ratio_list = []

for itercell in all_pref_df.iterrows():    # Iterate over DataFrame rows as (index, Series) pairs.
    try:
        cell_info = itercell[1]
        color_pref = color_hex[int(cell_info.color_pref) - 13]

        poly = Polygon([tuple(l) for l in list(segment_dict[cell_info.exp_ach][int(cell_info.cell)])])
        
# Plot hue selective with visual responsive cells together. PL        
        if cell_info.p_color < 0.01:
            ax.add_patch((PolygonPatch(poly, facecolor=color_pref, edgecolor=color_pref, zorder=999)))
        elif cell_info.p_vresp_ach < 0.01:
            ax.add_patch((PolygonPatch(poly, facecolor='gray', edgecolor='gray')))

# Plot hue selective cells based on the ANOVA of hue and orisf(achro), at least one of them has p < 0.01   PL
        if cell_info.p_color < 0.01:
            if cell_info.max_color_response > cell_info.r_pref_ach:
                # ax_hue_osi.add_patch((PolygonPatch(poly, facecolor=color_pref, edgecolor=color_pref,
                #                            alpha=cell_info.osi_color, zorder=999)))
                ax_hue_osi.add_patch((PolygonPatch(poly,
                                                   facecolor=cmap_patch_osi(cell_info.osi_color),
                                                   edgecolor=cmap_patch_osi(cell_info.osi_color),
                                                   zorder=999)))
            else:
                # ax_hue_osi.add_patch((PolygonPatch(poly, facecolor=color_pref, edgecolor=color_pref,
                #                            alpha=cell_info.osi_ach, zorder=999)))
                ax_hue_osi.add_patch((PolygonPatch(poly,
                                                   facecolor=cmap_patch_osi(cell_info.osi_ach),
                                                   edgecolor=cmap_patch_osi(cell_info.osi_ach),
                                                   zorder=999)))
        elif cell_info.p_vresp_ach < 0.01:
            if cell_info.max_color_response > cell_info.r_pref_ach:
                ax_hue_osi.add_patch((PolygonPatch(poly,
                                                   facecolor=cmap_patch_osi(cell_info.osi_color),
                                                   edgecolor=cmap_patch_osi(cell_info.osi_color))))
            else:
                ax_hue_osi.add_patch((PolygonPatch(poly,
                                                   facecolor=cmap_patch_osi(cell_info.osi_ach),
                                                   edgecolor=cmap_patch_osi(cell_info.osi_ach))))

# Plot visual reponsive cells based on color response vs achro response. PL
        if (cell_info.p_color < 0.01) or (cell_info.p_vresp_ach < 0.01):
            if cell_info.max_color_response > cell_info.r_pref_ach:
                if cell_info.osi_color > 1:
                    cell_info.osi_color = 1
                ax_osi.add_patch((PolygonPatch(poly,
                                              facecolor=cmap_patch_ori(cell_info.ori_pref_color/180),
                                              edgecolor=cmap_patch_ori(cell_info.ori_pref_color/180),
                                              zorder=999, alpha=cell_info.osi_color)))
            else:
                if cell_info.osi_ach > 1:
                    cell_info.osi_ach = 1
                ax_osi.add_patch((PolygonPatch(poly,
                                              facecolor=cmap_patch_ori(cell_info.ori_pref_ach/180),
                                              edgecolor=cmap_patch_ori(cell_info.ori_pref_ach/180),
                                              zorder=999, alpha=cell_info.osi_ach)))
            # Calculate CPI, color vs achromatic
            cell_ca_ratio = (cell_info.max_color_response - cell_info.r_pref_ach) / \
                            (cell_info.max_color_response + cell_info.r_pref_ach)
            if cell_ca_ratio < 1:   # Plot all ??? 
                ax_ca.add_patch((PolygonPatch(poly,
                                              facecolor=cmap_patch(((cell_ca_ratio/2) + 0.5)),
                                              edgecolor=cmap_patch(((cell_ca_ratio/2) + 0.5)),
                                              zorder=999)))
    except:
        continue

ax.set_xlim([0, 796])
ax.set_ylim([512, 0])
ax.xaxis.set_ticks_position('none')
ax.set_xticklabels([])
ax.yaxis.set_ticks_position('none')
ax.set_yticklabels([])
ax.set_aspect('equal')
ax.set_rasterized(True)
fig.savefig('%s/%s/color_ach_map_%s_%s.eps' % (folder, animal, animal, units[0]),
            format='eps', dpi=600)
fig.show()

ax_hue_osi.set_xlim([0, 796])
ax_hue_osi.set_ylim([512, 0])
ax_hue_osi.xaxis.set_ticks_position('none')
ax_hue_osi.set_xticklabels([])
ax_hue_osi.yaxis.set_ticks_position('none')
ax_hue_osi.set_yticklabels([])
ax_hue_osi.set_aspect('equal')
ax_hue_osi.set_rasterized(True)
fig_hue_osi.savefig('%s/%s/color_ach_osi_map_%s_%s.eps' % (folder, animal, animal, units[0]),
                    format='eps', dpi=600)
fig_hue_osi.show()

ax_ca.set_xlim([0, 796])
ax_ca.set_ylim([512, 0])
ax_ca.xaxis.set_ticks_position('none')
ax_ca.set_xticklabels([])
ax_ca.yaxis.set_ticks_position('none')
ax_ca.set_yticklabels([])
ax_ca.set_aspect('equal')
ax_ca.set_rasterized(True)
fig_ca.savefig('%s/%s/color_ach_ratio_map_%s_%s.eps' % (folder, animal, animal, units[0]),
               format='eps', dpi=600)
fig_ca.show()

ax_osi.set_xlim([0, 796])
ax_osi.set_ylim([512, 0])
ax_osi.xaxis.set_ticks_position('none')
ax_osi.set_xticklabels([])
ax_osi.yaxis.set_ticks_position('none')
ax_osi.set_yticklabels([])
ax_osi.set_aspect('equal')
ax_osi.set_rasterized(True)
fig_osi.savefig('%s/%s/color_ach_ori_map_%s_%s.eps' % (folder, animal, animal, units[0]),
               format='eps', dpi=600)
fig_osi.show()
