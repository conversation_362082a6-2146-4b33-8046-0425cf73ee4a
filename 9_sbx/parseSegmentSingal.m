% PL: Parse old 2P segmentation and signal data. 
% Because the old segmentation method could generate a dot, not a region,
% so the extracted signal is NaN. The code here is to remove those 'dots'
% in segmentation file, and remove 'NaN' in signal files.

%% Load data
clear
clc

dataFolder = 'H:\AE7\2P_data\U011\011_000';

resultFolder = strcat(dataFolder, '\NewData\');
if ~isfolder(resultFolder)
    mkdir(resultFolder);
end 
cd(dataFolder)

dataFiles = dir(strcat(dataFolder, '\*.segment'));
numFile = size(dataFiles,1);

for k=1:numFile

    dataFiles(k).name

    seg = load(fullfile(dataFiles(k).folder, dataFiles(k).name),'-mat');
    vert =seg.vert;
    mask = seg.mask;
    sz = size(vert,2);

    signame = strcat(dataFiles(k).name(1:11),'.signals');
    signal = load(fullfile(dataFiles(k).folder, signame),'-mat');
    sig = signal.sig;
    spks = signal.spks;

    ls=[];
%     for ii=1:sz
%         vt = vert(ii);   
%         legt=size(vt{1},1);
%         if legt == 1
%             ls(end+1) = ii;
%         end
%     end
    for ii=1:sz
        sg = sig(1,ii);  
        if isnan(sg)
            ls(end+1) = ii;
        end
    end

    vert(:,ls)=[];
    sig(:,ls)=[];
    spks(:,ls)=[];
    save([resultFolder,dataFiles(k).name], 'mask', 'vert', '-v7.3')
    save([resultFolder,signame], 'sig', 'spks', '-v7.3')
end