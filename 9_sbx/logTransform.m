

% Transform hartley log file to Dar<PERSON>'s style


% Read in hartley log file
animal = 'AF4';
% unit = '005';
% plane = '001';
Drive = 'O:';

dataRoot = strcat(Drive, '\', animal);
logRoot = strcat(dataRoot, '\2P_data', '\StimLogFile\');
analyerRoot = strcat(dataRoot, '\AnalyzerFiles\', animal, '\');

cd(logRoot)
resultFolder = strcat(logRoot, 'parsed');
if ~isfolder(resultFolder)
    mkdir(resultFolder);    
end

dataFiles = dir(strcat(logRoot, '*.mat'));
numFiles = size(dataFiles, 1);

for fn = 1:numFiles
    dataFiles(fn).name
    fileName = strcat(dataFiles(fn).name(end-14:end-4));
    fileNameSave = strcat(dataFiles(fn).name(end-14:end-4), '_log');
    log = load(dataFiles(fn).name);
    analyzer =  load(strcat(analyerRoot, fileName(1:4), 'u', fileName(5:end), '.analyzer'), '-mat');
    xsize =  analyzer.Analyzer.P.param{1, 6}{1,3};
    ysize =  analyzer.Analyzer.P.param{1, 7}{1,3};
    hper = analyzer.Analyzer.P.param{1, 17}{1, 3}; 
    stimSize = [xsize, ysize];
    frate = round(log.frate);
    log = rmfield(log,'frate');
    fields = fieldnames(log);
    trialNum0 = length(analyzer.Analyzer.loops.conds);
    trialNum = length(fields);
    if trialNum0 ~= trialNum
        warning(strcat(fileName, ': Some trial is missing!!'))
    end
    frameNum = length (log.randlog_T1.seqs.frameseq);
    logFile =  nan(trialNum*frameNum, 4);
    logFile(:,1) = [0:hper:(trialNum*frameNum-1)*hper]';
    count = 1;
    for tn = 1: trialNum
        for frn = 1: frameNum
            stimFrame = log.(fields{tn}).seqs.frameseq(frn);
            kx = log.(fields{tn}).domains.Cond(stimFrame, 2);
            ky = log.(fields{tn}).domains.Cond(stimFrame, 3);
            bwdom = log.(fields{tn}).domains.Cond(stimFrame, 4);
            [c] = parsehartley(kx, ky, bwdom,stimSize);
%             logFile(count, 1) = stimFrame;
            logFile(count, 2) = c.Ori;
            logFile(count, 3) = c.SpatialPhase;
            logFile(count, 4) = c.SpatialFreq;
            count = count+1;
        end
    end
%     logFile2 = cell(1, trialNum*frameNum);
%     for ii = 1:size(logFile,1)
%         logFile2{ii} = table(logFile(ii,1),logFile(ii,2),logFile(ii,3),logFile(ii,4),...
%         'VariableNames',{'Stimframe' 'ori' 'sphase' 'sper'});
%     end
%     save(fileName, 'logFile2')
    save(fullfile(resultFolder, fileNameSave), 'logFile')
%     fid = fopen(strcat(fileName, '.log_31'),'wt');
%     mesag = 'T 0';
%     fprintf(fid,'%s \n', mesag);
%     for ii = 1:size(logFile,1)
%         if ~isnan(logFile(ii, 2))
%             fprintf(fid,'%9.2f \t',logFile(ii,:));
%             fprintf(fid,'\n');
%         end
%     end
%     fclose(fid);
end


% Reorganize log file format


% # sf component at x and y axis in Fourier plane
% all_trials['sf_x'] = np.round(np.nan_to_num(all_trials['sf'] * np.cos(all_trials['ori'])), 1)   # Wx
% all_trials['sf_y'] = np.round(np.nan_to_num(all_trials['sf'] * np.sin(all_trials['ori'])), 1)   # Wy
% all_trials['sbxframe'] = trial_samples_corrected.astype(int)


% Save new log file



