function [r,stat] = sbxRevFourier(fname)

% edit Peichao 7/10/19.     
% analyze hartely experiment
% Allow rigid and nonrigid .signals files as input..
% The log file needs to be transformed from our hartley log file

% Input: signal extracted from aligned data (_rigid.sbx)
% log file of stimulus
% 

%% -----
if contains(fname,'rigid') % search for rigid in filename
    si = strfind(fname,'_');   
    fnamelog = fname( 1:si(end)-1); % remove it; PL: this is the name of log file (stim frame, ori, phase, nper)
else 
    fnamelog = fname;
end

%%
% PL. the sbxreadorisflog adds the scanbox frames (2P frame) into log file to align the stimulus frame and scan frame. 
% PL: polyfit(stim_frame,scanbox_frame, 1), The alignment method is ployfitting the stim frame and scan frame, then take the fitted value. 
% PL: Make sure these two frame streams are the same length, because we are scanning two planes.
% log = sbxreadorisflog(fnamelog); % read log;
% log = log{1};       % assumes only 1 trial, now data format is table.   % PL: for us, hartley is not one trial under 2P imaging
logFile_whole = logFile;
loca = [];
count = 1;
for ii = 1:size(logFile_whole,1)
    if isnan(logFile(ii, 2))
        loca(count) = ii;
        count = count+1;
    end
end

logFile(loca,:) = [];
max_ori = length(unique(logFile(:,2)));   % 
max_sphase = length(unique(logFile(:,3))); 
max_sper = length(unique(logFile(:,4)));

log = array2table(logFile_whole);
log.Properties.VariableNames(1:5) = {'stimFrame','ori','sphase','sf', 'sbxframe'};


load(['AF4_004_001_ot_001.signals'],'-mat');    % load signals

dsig = spks;   % load inferred spikes 

ncell = size(dsig,2);   % number of cells
nstim = size(log,1);   % Number of stimuli

ntau = 20;   

r = zeros(max_ori,max_sphase,max_sper,ntau,ncell);  % Initalize a matrix to store 
N = zeros(max_ori,max_sphase,max_sper);

[temp, ori_ranked] = ismember(log.ori,unique(log.ori));
[temp, sphase_ranked] = ismember(log.sphase,unique(log.sphase));
[temp, sf_ranked] = ismember(log.sf,unique(log.sf));
sf_ranked = sf_ranked-1;

h = waitbar(0,'Processing...');
for i = 1:nstim
    if ~isnan(logFile_whole(i,2))
        r(ori_ranked(i),sphase_ranked(i),sf_ranked(i),:,:) =  ... 
            squeeze(r(ori_ranked(i),sphase_ranked(i),sf_ranked(i),:,:)) + ...
            dsig(log.sbxframe(i)-2:log.sbxframe(i)+ntau-3,:);
         N(ori_ranked(i),sphase_ranked(i),sf_ranked(i)) = ...
            N(ori_ranked(i),sphase_ranked(i),sf_ranked(i)) + 1; 
    end
    waitbar(i/nstim,h);
end

%% Normalize by stim count 
for(t=1:ntau)
    for(n = 1:ncell)
        r(:,:,:,t,n) = r(:,:,:,t,n)./N;
    end
end

%% average across spatial phase
R = r;
r = squeeze(nanmean(R,2));

% now r = r(ori,sper,time,cell)  % 4 dimention
h = fspecial('gauss',5,1);
disp('Filtering');
k = 0;
for(t=1:ntau)
    for(n = 1:ncell)
        rf = squeeze(r(:,:,t,n));
        rf2 = [rf(end-1,:); rf(end,:); rf ;rf(1,:) ; rf(2,:)];   % PL: padding
        rf3 = [rf2(:,1) rf2(:,1) rf2 rf2(:,end) rf2(:,end)];     % PL: padding
        rf4 = filter2(h,rf3,'valid');    % filtering
        r(:,:,t,n) = rf4;   % save results
        k = k+1;    % counting
    end
end


% param values

ori = 0:10:170;
% sper = 1920./(1.31.^(0:11));   % spatial period in pixels
% sper_pix = sper;
% sper = sper / 15.25;           % 15.25 pixels per degree 
% sf = 1./sper;                  % cycles/deg
sf = [0.05:0.05:6];
sphase = linspace(0,360,9);    % sphase =0:45:315;
sphase = sphase(1:end-1);


%%

disp('Statistics...')

% [xx,yy] = meshgrid(-12:12,-12:12);
% zz = xx+1i*yy;
% zz = abs(zz).*exp(1i*angle(zz)*2);

% calculate for each case...


% for i = 1:size(r,4)
i = 10;
    z = squeeze(r(:,:,:,i));    % PL: choose one cell, so only ori, sf and ntau dimension
%     q = reshape(z,max_ori*max_sper,[]);   % PL: each ori and sf combination is one row, each tau is one column. q is a matrix whose columns are random variables (for each tau) and whose rows are observations (of ori and sf combinations).
    q = reshape(z,61*61,[]);
    k = std(q);   % PL: k is a row vector containing the standard deviations corresponding to each column.
    [kmax,t] = max(k);  % PL: max std (kmax) and its index (t/tau) (which ori and sf combination of the tau is the best for this cell)
    tmax = t;     % PL: tmax is the best tau
    stat(i).k = k; 
    stat(i).tmax = tmax;
    stat(i).kmax = kmax;
    stat(i).kern = squeeze(z(:,:,4));  % PL: the ori and sf combination (stimulus) of best tau

    %PL: plot the tuning of orisf at best tau.
%     imagesc(log10(sf),ori,stat(i).kern);
     imagesc(stat(i).kern);
%     xlabel('Spatial Frequency (cycles/deg)');
%     ylabel('Orientation (deg)');
%     xval = get(gca,'xtick');
%     l = cell(length(xval),1);
%     for k = 1:length(l)
%         l{k} = sprintf('%.2f',10^xval(k));
%     end
%     set(gca,'xticklabel',l);
    
% separability measure
    [u,s,v] = svd(stat(i).kern);    
    stat(i).lambda12 = s(1,1)/s(2,2);
    
    % energy measure
    
    stat(i).delta = max(stat(i).k)/stat(i).k(1);
    stat(i).sig = stat(i).delta>1.75;
    
    % estimate preferred ori and sf
    
    q = s(1,1);
    s = zeros(size(s));
    s(1,1) = q;
    kest = u*s*v';
    stat(i).kest = kest;
    
%     resp_sf = mean(kest);
%     resp_ori = mean(kest');
    
    [ii,jj] = find(stat(i).kern == max(stat(i).kern(:)));   % take slices through max
    
    resp_sf = stat(i).kern(ii(1),:);
    resp_ori = stat(i).kern(:,jj(1))';

    stat(i).resp_sf = resp_sf;
    stat(i).resp_ori = resp_ori;
    
    
    
    resp_sf = resp_sf - max(resp_sf)*.75; % clip tails...
    resp_sf(resp_sf<0) = 0;
    
%     stat(i).ori_est = rad2deg(angle(sum(resp_ori.*exp(1i*ori*2*pi/180)))/2);
%     if(stat(i).ori_est<0)
%         stat(i).ori_est = stat(i).ori_est+180;
%     end
%     stat(i).sf_est  = 10^(sum(resp_sf.*log10(sf))/sum(resp_sf));
    
%     hold on
%     plot(log10(stat(i).sf_est),stat(i).ori_est,'k.','markersize',15);
%     hold off;
    
    title(sprintf('Cell #%d',i));
        
 
    % try to reconstruct linear rf - decimate by 4

%     [xx,yy] = meshgrid((1:1920/4)-1920/8,(1:1080/4)-1080/8);
%     rf = zeros(size(xx));
%     sp = sper_pix/4;
%     for m=1:max_ori
%         for j=1:max_sper
%             for k=1:max_sphase
%                 if(N(m,k,j)>0)
%                     sth = sind(ori(m));
%                     cth = cosd(ori(m));
%                     stim = cos (2*pi * (cth*xx + sth*yy) / sp(j) + sphase(k));
%                     rf = rf + R(m,k,j,stat(i).tmax,i)*stim;
%                 end
%             end
%         end
%     end
% 
%     subplot(1,2,2)
%     imagesc(rf)
%     axis off;
    
   pause(0.1);
   
% end
%%
ori = 0:10:170;
sper = 1920./(1.31.^(0:12));   % spatial period in pixels
sper = sper / 9.6;             % 9.6 pixels per degree near center of screen
sf = 1./sper;               % cycles/deg
sphase = linspace(0,360,9);
ph = sphase(1:end-1);


save([fname '.orisf'],'r','stat','ori','sf','ph','-v7.3');
disp('Done!');



