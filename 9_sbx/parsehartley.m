function [c]=parsehartley(kx,ky,bwdom,size)
    %kx=p(2);ky=p(3);bwdom=p(4);%colordom=p(5);oridom = p(1);
    if kx==0 && ky~=0   % 0
        c.Ori=0;
    elseif ky==0 && kx~=0   % 90
        c.Ori=90;
    elseif kx*ky > 0   % (90 180)
        c.Ori = 180 - atand(ky/kx);
    elseif kx*ky < 0   % (0 90)
        c.Ori = abs(atand(ky/kx));
    else
        c.Ori=NaN;
    end
    c.SpatialFreq = sqrt((kx/size(1))^2 + (ky/size(2))^2);

    if ky>=0
        q=0;
        if kx<0 && ky==0
            q=0.25;
        end
    else
        q=0.25;
    end
    if bwdom==-1
        q=q+0.5;
    end
    c.SpatialPhase = q;
end