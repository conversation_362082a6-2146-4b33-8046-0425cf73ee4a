function log = sbxreadorisflogPL(fname)

fn = ['sa2_003_009.log_31'];
fid = fopen(fn,'r');
l = fgetl(fid);
k=0;

log = {};
while(l~=-1)
    k = str2num(l(2:end));
    log{k+1} = [];
    e = [];
    l = fgetl(fid);
    while(l~=-1 & l(1)~='T')
        e = [e ; str2num(l)];
        l = fgetl(fid);
    end
    log{k+1} = table(e(:,1),e(:,2),e(:,3),e(:,4),...
    'VariableNames',{'Stimframe' 'ori' 'sphase' 'sper'});
end

sbx = load('AF4_004_001_ot_001_rigid.mat');

frame_diff = diff(sbx.info.frame);
frame_reset = find(frame_diff < 0)+1;
for ii = frame_reset:length(sbx.info.frame)
    sbx.info.frame(ii) = sbx.info.frame(ii) + 65536;
end
        
numPlanes = sbx.info.otparam(3);
scanbox_frame1 = sbx.info.frame/numPlanes;
scanbox_frame = double(struct.sbxframe)';
logFile(:,5) = scanbox_frame;
% add sbxframe to all logs...

k = 1;
for j = 1: length(logFile)
    ov_frame = logFile(j,1);
    fitted = polyfit(ov_frame,scanbox_frame(k:k+length(ov_frame)-1),1);           % from ov to sbx
    t = floor(polyval(fitted,ov_frame));
    logFile(j,5) = t;
end

