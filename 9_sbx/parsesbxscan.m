function info = parsesbxscan(fname,info)


% #pmts is the number of pmt channels being sampled (1 or 2)
% rows is the number of lines in the image
% cols is the number of pixels in each line
%
% The function also creates a global 'info' variable with additional
% informationi about the file


if(exist([fname ,'.align'])) % aligned?
    info.aligned = load([fname ,'.align'],'-mat');
else
    info.aligned = [];
end   

% Remove extra trigger. PL
if info.frame(1) == 0
    info.frame = info.frame(2:end);
end
if  info.line(1) == 0
    info.line=info.line(2:end);
end


if(~isfield(info,'sz'))
    info.sz = [512 796];    % it was only sz = .... 
end

if(~isfield(info,'scanmode'))
    info.scanmode = 1;      % unidirectional
    recordsPerBuffer = info.recordsPerBuffer;
end

if(info.scanmode==0)   % bidirectional
    info.recordsPerBuffer_bi = info.recordsPerBuffer*2;   % PL, this is for bidirectional, it is better to distinguish here
    recordsPerBuffer = info.recordsPerBuffer_bi;
elseif (info.scanmode == 1)      % unidirectional
    recordsPerBuffer = info.recordsPerBuffer;
end

switch info.channels
    case 1
        info.nchan = 2;      % both PMT0 & 1
        factor = 1;
    case 2
        info.nchan = 1;      % PMT 0
        factor = 2;
    case 3
        info.nchan = 1;      % PMT 1
        factor = 2;
end

info.fid = fopen([fname '.sbx']);
d = dir([fname '.sbx']);
info.nsamples = (info.sz(2) * recordsPerBuffer * 2 * info.nchan);   % bytes per record 
%Edit Patrick: to maintain compatibility with new version

if isfield(info,'scanbox_version') && info.scanbox_version >= 2
    info.max_idx =  d.bytes/recordsPerBuffer/info.sz(2)*factor/4 - 1;
%         info.nsamples = (info.sz(2) * info.recordsPerBuffer * 2 * info.nchan);   % bytes per record 
else
    info.max_idx =  d.bytes/info.bytesPerBuffer*factor - 1;
end

info.totalFrame = info.max_idx+1;
% Correct error for experiments that have more than 65536 frames
frameDiff = diff(info.frame);
frameReset = find(frameDiff < 0);
for i = 1:length(frameReset)
    info.frame(frameReset(i)+1:end) = info.frame(frameReset(i)+1:end)+65536;   % 2^16
end



