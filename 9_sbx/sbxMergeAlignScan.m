%Peichao
% Integrate .align files into .scan files


%%
clear
dataRoot = 'H:\AE7\2P_data\U014\';
% folder = dir(dataRoot);
% subFolder = folder([folder.isdir]);
% numFiles = size(subFolder, 1);

% dataFiles = dir(strcat(dataRoot, '*', plane,'.segment'));
dataFiles = dir(strcat(dataRoot, '*.align'));
numFiles = size(dataFiles, 1);
cd(dataFiles(1).folder)

for i = 1:numFiles
    dataFiles(i).name
    scName = [dataFiles(i).name(1:11),'_parse.scan'];
%     load([fname '.segment'],'-mat'); % load segmentation
    load(scName, '-mat'); % load info here, I don't like to use global. This info should be parsed.
    
%     if strcmp(dataFiles(i).name(16:18), '000')
    info.alignpl0= load(dataFiles(i).name, '-mat');
%     elseif strcmp(dataFiles(i).name(16:18), '001')
%         info.alignpl1 = load(dataFiles(i).name, '-mat');
%     end
    
    save(scName, 'info');
end