%% User Inputs

% Expt info.
clc;
clear;
dataFolder = '/media/vcl/VCL004/AG4/2P_data/U007/';
cd(dataFolder)
dataFiles = dir(strcat(dataFolder, '*.signals'));

planeNum = str2double(dataFiles(end).name(16:18))+1;
% planeNum = 1;

numFiles = size(dataFiles, 1);

for i = 1:planeNum:numFiles
    fname = dataFiles(i).name
    
    folderName = fname(5:11);
    resultFolder = strcat(dataFolder, folderName, '/');
    if ~isfolder(resultFolder)
        mkdir(resultFolder);    
    end
    sbxmerge(fname(1:11), resultFolder)
end


