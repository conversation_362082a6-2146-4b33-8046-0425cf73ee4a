% Peichao
% Only for adding .sling file into .scan file
% I usualy do this during puuling signal, but sometimes it need to be done separately.

clc
clear;
dataFolder = 'J:\AF3\2P_data\U003\';
plane = '000';
cd(dataFolder)
% dataFiles = dir(fullfile(dataFolder, '**', ['*', plane ,'.align']));
dataFiles = dir(fullfile(dataFolder, ['*', plane ,'.align']));
multiplane = 1;

numFiles = size(dataFiles, 1);

for i = 1:numFiles
    
    fname = dataFiles(i).name
    cd(dataFiles(i).folder)
    
    unit = dataFiles(i).name(5:7);

    if multiplane == 1
        scanName = [fname(1:15),'parse.scan'];
    elseif multiplane == 0
        scanName = [fname,'_parse.scan'];
    end

    load(scanName, '-mat'); % load info (.scan file)
    aligned = load(fname, '-mat'); % load .align file

    if multiplane == 1
        nameplane = str2double(fname(16:18));
        info.(sprintf('alignpl%d', nameplane)) = aligned;
    elseif multiplane == 0
        info.alignpl0 = aligned;
    end

    save(scanName, 'info');
end



