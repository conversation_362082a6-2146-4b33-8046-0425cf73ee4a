%Peichao
% First step of parsing scanning info.mat. Split the data if this is multiplane scanning.

% Expt info.
clear;
clc;
dataFolder = '/media/peichao/My Passport/AG3_masktest/';
multiplane = 1; % 0: single plane, no need to split; 1: multiplanes, need split. I usually scan 2 planes.
doSplit = 0; % 0: do not split .sbx, just parse info.mat; 1: split .sbx file and parse info.mat

dataFiles = dir(strcat(dataFolder, '*.sbx'));
cd(dataFolder)
numFiles = size(dataFiles, 1);

for i = 1:numFiles
    
    fname = dataFiles(i).name(1:end-4)
%     dataPath = fullfile(dataFiles(i).folder, [dataFiles(i).name(1:end-4), '.mat']);
    load(fname)
    if multiplane == 1
        if doSplit == 1
            sbxsplit(info,fname)
        elseif doSplit == 0
            sbxsplit_matfile(info,fname)
        end
    elseif multiplane == 0
        info = parsesbxscan(fname, info);  % PL: parse scan info.
        matname = sprintf('%s_parse.scan',fname);
        save(matname, 'info')
    end
end


