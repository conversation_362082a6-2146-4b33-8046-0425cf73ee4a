function sbxsuite2p(fname)
 
str = [];
 
% if iscell(fname)
%     for i=1:length(fname)
%         str = [str ' ' getpath(fname{i})];
%     end
% else
%     str = getpath(fname);
% end

datapath = '/media/peichao/PL_NHP_AE7AE8/AF4/AF4_004_005_ot_001.sbx';

str = strrep(datapath,'\','/');
pyscript = strrep(which('suite2p_process.py'),'\','/');     % find the processing script
cmd = sprintf('conda activate suite2p && python.exe %s %s', pyscript, str);
system('conda init powershell')
system('conda activate suite2p')
system(cmd);
 
 
% function p = getpath(fn)
%  
% try
%     s = what(fn);
%     p = s.path;
% catch
%     error('No such file');
% end