function [r,stat] = sbxhartley(fname)

% analyze sparse noise experiment

% edit Luis 1/10/17. Allow rigid and nonrigid .signals files as input..
    % -----
    if contains(fname,'rigid') % search for rigid in filename
        si = strfind(fname,'_'); 
        fnamelog = fname( 1:si(end)-1); % remove it
    else 
        fnamelog = fname;
    end
    % -----

%%
log = sbxreadhartleylog(fnamelog); % read log
log = log{1};       % assumes only 1 trial
max_k = max(abs(log.kx));

load([fname, '.signals'],'-mat');    % load signals

% Dario - now signals are deconvolved in the segmentation tool
% sig = medfilt1(sig,11);             % median filter
% sig = zscore(sig);
% dsig = diff(sig);    
% p = prctile(dsig,65);
% dsig = bsxfun(@minus,dsig,p);
% dsig = dsig .* (dsig>0);
% dsig = zscore(dsig);

dsig = spks;

ncell = size(dsig,2);
nstim = size(log,1);

ntau = 20;

%% Build a matrix to store the signal according to the stimulus. kx(ky) is symmetrical to x(y) axis.
r = zeros(2*max_k+1,2*max_k+1,ntau,ncell);   

% Arrange signals in hartley space according to the stimulus.
h = waitbar(0,'Processing...');
for(i=1:nstim)
        r(log.kx(i)+1+max_k,log.ky(i)+1+max_k,:,:) =  squeeze(r(log.kx(i)+1+max_k,log.ky(i)+1+max_k,:,:)) + ...       % Note that kx(ky) is symmetrical to 0, so add max_k will make -kx to x(y) axis.
            dsig(log.sbxframe(i)-2:log.sbxframe(i)+ntau-3,:);  % Pick up the signal and store it into matrix (hartley space) and with tau as 3rd demensions. Acquired signals corresponding to the stimulus 2 frames (130ms) ago.
    waitbar(i/nstim,h);
end
delete(h);

%% PL: Filter the signal of each frame(tau) in the plane of kx-ky(hartley space).  
% PL: Assume a V1 cell has smooth orientation and sf tuning curve, so filtering here in hartley space is the same to smoothing ori/sf tuning curve
h = fspecial('gauss',5,1);  % Filter kernel, gaussian filter
% hh = waitbar(0,'Filtering...');
k = 0;
for(t=1:ntau)
    % for(n = 1:ncell)
        rf = squeeze(r(:,:,t,n));     % extract the ntau-th frame
        % PL: rotates array frame counterclockwise by 2*90 degrees. Because hartley space is symmertrical to origin, two symmertrical point in hartley space have the same ori and sf, but 90deg-shifted phase.
        rf = rf + rot90(rf,2); % symmetry.  PL: this step averages conditions have the same sf and ori, but different phases.
        r(:,:,t,n) = filter2(h,rf,'same');    
        k = k+1;
        % waitbar(k/(13*2*ncell),hh);
    % end
end
delete(hh);

hh = waitbar(0,'Statistics...');

[xx,yy] = meshgrid(-30:30,-30:30);   % PL: sholud be the same size as hartley space (-kxmax:kxmax, -kymax:kymax)
zz = xx+1i*yy;    
zz = abs(zz).*exp(1i*angle(zz)*2);   % Transform the hartley space into complex plane, then the real and imaginary part encode orientation o

for i = 1:size(r,4)   % number of cells
    z = squeeze(r(:,:,:,i));
    q = reshape(z,61^2,[]);   % in this case, there are 61^2 pixels in the stimulus.
    k = kurtosis(q)-3;   % The kurtosis of any univariate normal distribution is 3. It is common to compare the kurtosis of a distribution to this value.
    [kmax,t] = max(k);
    tmax = t-3;    % ??
    stat(i).k = k;
    stat(i).tmax = tmax;
    stat(i).kmax = kmax;
    stat(i).kern = squeeze(z(:,:,t));
    stat(i).sig = (kmax>7);
    
    % estimate ori/sf
    bw = stat(i).kern>(max(stat(i).kern(:))*.95);
    idx = find(bw);
    zzm = mean(zz(idx));
    zzm = abs(zzm)*exp(1i*angle(zzm)/2);
    
    stat(i).sf = abs(zzm);
    stat(i).ori = angle(zzm);
    
    if(stat(i).sig)
        clf
        imagesc(-30:30,-30:30,stat(i).kern);
        hold on;
        plot([0 real(zzm)],[0 imag(zzm)],'wo-','linewidth',3,'markersize',14);
%         pause(1);
    end
end

delete(hh);

save([fname '.hartley'],'r','stat','-v7.3');


