from suite2p import run_s2p, default_ops
import numpy as np
import sys, os
import matlab.engine
import scipy.io

data_dirs = sys.argv[1:]

ops = default_ops()					# load default options

for dd in data_dirs:				# batch suite-2p processing of each file

	print('\nProcessing: ' + dd + '\n')

	db = {
	      'input_format': 'sbx', 		# process sbx files
	      'delete_bin': True,			# delete bin 
	      'look_one_level_down': False, # whether to look in ALL subfolders when searching for sbx files
	      'fs': 15.56,					# typical sampling freq
	      'data_path': [dd], 			# a list of folders with data - just one here...  
	      'save_path': dd,				# save path                     
	      'subfolders': [], 			# choose subfolders of 'data_path' to look in (optional)
	      'save_mat': True,				# save matlab output
	      'fast_disk': 'h:/bin' 		# string which specifies where the binary file will be stored (should be an SSD)
	    }

	opsEnd = run_s2p(ops=ops, db=db)


for dd in data_dirs:									# Do post-processing 

	print('\nPost-processing: ' + dd)

	os.chdir(dd + '/suite2p/plane0/')					# change directory to where the results are
	fn = dd.split('/')
	fn = fn[len(fn)-1]

	eng = matlab.engine.start_matlab()					# start up your engines...
	eng.sbxsuite2sbx('Fall.mat',fn,0.8,nargout=0)		# convert back to Scanbox segment and signal files
	ops =  np.load('ops.npy', allow_pickle=True)		# save mean image in align file
	ops =  ops.item()
	m = ops['meanImg']
	os.chdir('../../')									# go back to base folder
	scipy.io.savemat(fn + '.align',{'m': m})			# save mean image in align file